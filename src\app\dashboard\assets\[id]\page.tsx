'use client'

import { useState, useEffect, useMemo } from 'react'
import { useParams } from 'next/navigation'
import {
  Shield,
  Search,
  Clock,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  ArrowLeft,
  Activity,
  TrendingUp,
  BarChart3,
  Target,
  Calendar,
  Timer,
  Database,
  Download,
  FileText,
  Edit,
  Trash2,
  RefreshCw,
  Filter,
  Eye,
  Copy,
  MoreHorizontal,
  Zap,
  Globe,
  Settings
} from 'lucide-react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  TimeScale,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import { PageContainer, PageHeader } from '@/components/layout'
import { PDFReportGenerator } from '@/components/reports/pdf-report-generator'
import { <PERSON><PERSON>, Alert, Card, CardContent, CardHeader, CardTitle, Badge, Input, Pagination } from '@/components/ui'
import { NoSSR } from '@/components/no-ssr'
import Link from 'next/link'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  TimeScale
)

interface AssetDetails {
  id: string
  url: string
  domain: string
  title: string
  description?: string
  status: string
  lastScanned?: string
  createdAt: string
  scans: Array<{
    id: string
    status: string
    startedAt?: string
    completedAt?: string
    duration?: number
    totalVulns: number
    criticalVulns: number
    highVulns: number
    mediumVulns: number
    lowVulns: number
    infoVulns: number
    unknownVulns?: number
  }>
  vulnerabilities: Array<{
    id: string
    templateId: string
    name: string
    severity: string
    description?: string
    host: string
    matchedAt: string
    timestamp: string
    scanId: string
  }>
}

interface FilterState {
  search: string
  severity: string
  scanId: string
  dateRange: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

interface AssetStats {
  totalScans: number
  totalVulnerabilities: number
  securityScore: number
  averageScanDuration: number
  scanFrequency: number
  lastScanDate?: string
  vulnerabilityTrends: Array<{
    date: string
    critical: number
    high: number
    medium: number
    low: number
    info: number
    total: number
  }>
  scanHistory: Array<{
    date: string
    scans: number
    vulnerabilities: number
  }>
}

export default function AssetDetailPage() {
  const params = useParams()
  const [asset, setAsset] = useState<AssetDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Enhanced filtering and pagination state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    severity: '',
    scanId: '',
    dateRange: '',
    sortBy: 'timestamp',
    sortOrder: 'desc'
  })

  const [vulnerabilityPage, setVulnerabilityPage] = useState(1)
  const [vulnerabilityLimit] = useState(10)

  useEffect(() => {
    if (params.id) {
      fetchAssetDetails(params.id as string)
    }
  }, [params.id])

  const fetchAssetDetails = async (assetId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/assets/${assetId}`)
      if (response.ok) {
        const data = await response.json()
        setAsset(data.asset)
      } else {
        setError('Failed to fetch asset details')
      }
    } catch (error) {
      setError('Failed to fetch asset details')
      console.error('Error fetching asset details:', error)
    } finally {
      setLoading(false)
    }
  }

  // Enhanced data processing
  const assetStats = useMemo(() => {
    if (!asset) return null

    const totalScans = asset.scans.length
    const totalVulnerabilities = asset.vulnerabilities.length
    const completedScans = asset.scans.filter(s => s.status === 'COMPLETED')
    const averageScanDuration = completedScans.length > 0
      ? completedScans.reduce((sum, scan) => sum + (scan.duration || 0), 0) / completedScans.length
      : 0

    // Calculate security score (0-100) based on vulnerability severity
    const criticalCount = asset.vulnerabilities.filter(v => v.severity.toUpperCase() === 'CRITICAL').length
    const highCount = asset.vulnerabilities.filter(v => v.severity.toUpperCase() === 'HIGH').length
    const mediumCount = asset.vulnerabilities.filter(v => v.severity.toUpperCase() === 'MEDIUM').length

    const securityScore = Math.max(0, 100 - (criticalCount * 20 + highCount * 10 + mediumCount * 5))

    // Calculate scan frequency (scans per month)
    const firstScan = asset.scans.length > 0 ? new Date(asset.scans[asset.scans.length - 1].startedAt || asset.createdAt) : new Date()
    const monthsDiff = (new Date().getTime() - firstScan.getTime()) / (1000 * 60 * 60 * 24 * 30)
    const scanFrequency = monthsDiff > 0 ? totalScans / monthsDiff : 0

    return {
      totalScans,
      totalVulnerabilities,
      securityScore,
      averageScanDuration,
      scanFrequency,
      lastScanDate: asset.lastScanned
    }
  }, [asset])

  // Chart data preparation
  const chartData = useMemo(() => {
    if (!asset) return null

    // Vulnerability severity distribution
    const severityCounts = asset.vulnerabilities.reduce((acc, vuln) => {
      const severity = vuln.severity.toUpperCase()
      acc[severity] = (acc[severity] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const severityChart = {
      labels: ['Critical', 'High', 'Medium', 'Low', 'Info', 'Unknown'],
      datasets: [{
        data: [
          severityCounts.CRITICAL || 0,
          severityCounts.HIGH || 0,
          severityCounts.MEDIUM || 0,
          severityCounts.LOW || 0,
          severityCounts.INFO || 0,
          severityCounts.UNKNOWN || 0
        ],
        backgroundColor: [
          '#dc2626', // Critical - red
          '#ea580c', // High - orange
          '#d97706', // Medium - amber
          '#2563eb', // Low - blue
          '#059669', // Info - green
          '#6b7280'  // Unknown - gray
        ],
        borderWidth: 0,
      }]
    }

    // Vulnerability trends over time (last 30 days)
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - i)
      return date.toISOString().split('T')[0]
    }).reverse()

    const trendData = last30Days.map(date => {
      const dayVulns = asset.vulnerabilities.filter(vuln =>
        vuln.timestamp.split('T')[0] === date
      )
      return {
        date,
        critical: dayVulns.filter(v => v.severity.toUpperCase() === 'CRITICAL').length,
        high: dayVulns.filter(v => v.severity.toUpperCase() === 'HIGH').length,
        total: dayVulns.length
      }
    })

    const trendsChart = {
      labels: trendData.map(d => new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
      datasets: [
        {
          label: 'Critical',
          data: trendData.map(d => d.critical),
          borderColor: '#dc2626',
          backgroundColor: 'rgba(220, 38, 38, 0.1)',
          tension: 0.4,
          fill: false,
        },
        {
          label: 'High',
          data: trendData.map(d => d.high),
          borderColor: '#ea580c',
          backgroundColor: 'rgba(234, 88, 12, 0.1)',
          tension: 0.4,
          fill: false,
        },
        {
          label: 'Total',
          data: trendData.map(d => d.total),
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
          fill: true,
        }
      ]
    }

    // Scan history timeline
    const scanHistory = asset.scans.map(scan => ({
      date: scan.startedAt || scan.completedAt || '',
      vulnerabilities: scan.totalVulns,
      status: scan.status
    })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    const historyChart = {
      labels: scanHistory.map(s => new Date(s.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
      datasets: [{
        label: 'Vulnerabilities Found',
        data: scanHistory.map(s => s.vulnerabilities),
        backgroundColor: scanHistory.map(s =>
          s.status === 'COMPLETED' ? '#10b981' :
          s.status === 'FAILED' ? '#ef4444' : '#f59e0b'
        ),
        borderRadius: 4,
      }]
    }

    return { severity: severityChart, trends: trendsChart, history: historyChart }
  }, [asset])

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: { size: 12 }
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: 'rgba(0, 0, 0, 0.1)' },
        ticks: { font: { size: 11 } }
      },
      x: {
        grid: { color: 'rgba(0, 0, 0, 0.1)' },
        ticks: { font: { size: 11 } }
      }
    }
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: { size: 12 }
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      }
    },
    cutout: '60%',
  }

  // Helper functions
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleExport = async (format: 'json' | 'csv') => {
    if (!asset) return

    try {
      // Build export URL with current filters
      const params = new URLSearchParams({
        format
      })

      if (filters.severity) {
        params.append('severity', filters.severity)
      }

      if (filters.search) {
        params.append('search', filters.search)
      }

      const response = await fetch(`/api/export/assets/${asset.id}/vulnerabilities?${params}`)

      if (!response.ok) {
        throw new Error('Export failed')
      }

      if (format === 'csv') {
        // For CSV, the response is already formatted as CSV content
        const csvContent = await response.text()
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `asset-${asset.id}-vulnerabilities-${new Date().toISOString().split('T')[0]}.csv`
        a.click()
        URL.revokeObjectURL(url)
      } else {
        // For JSON, get the data and create blob
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `asset-${asset.id}-report-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export failed:', error)
      // You might want to show a toast notification here
    }
  }

  // Enhanced filtering logic
  const filteredVulnerabilities = useMemo(() => {
    if (!asset?.vulnerabilities) return []

    let filtered = [...asset.vulnerabilities]

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(vuln =>
        vuln.name.toLowerCase().includes(searchLower) ||
        vuln.templateId.toLowerCase().includes(searchLower) ||
        vuln.host.toLowerCase().includes(searchLower) ||
        (vuln.description && vuln.description.toLowerCase().includes(searchLower))
      )
    }

    // Severity filter
    if (filters.severity) {
      filtered = filtered.filter(vuln => vuln.severity.toUpperCase() === filters.severity.toUpperCase())
    }

    // Scan ID filter
    if (filters.scanId) {
      filtered = filtered.filter(vuln => vuln.scanId === filters.scanId)
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof typeof a]
      let bValue: any = b[filters.sortBy as keyof typeof b]

      if (filters.sortBy === 'timestamp') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      } else if (filters.sortBy === 'severity') {
        const severityOrder = { CRITICAL: 5, HIGH: 4, MEDIUM: 3, LOW: 2, INFO: 1, UNKNOWN: 0 }
        aValue = severityOrder[aValue?.toUpperCase() as keyof typeof severityOrder] || 0
        bValue = severityOrder[bValue?.toUpperCase() as keyof typeof severityOrder] || 0
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return filtered
  }, [asset?.vulnerabilities, filters])

  // Pagination for vulnerabilities
  const paginatedVulnerabilities = useMemo(() => {
    const startIndex = (vulnerabilityPage - 1) * vulnerabilityLimit
    const endIndex = startIndex + vulnerabilityLimit
    return filteredVulnerabilities.slice(startIndex, endIndex)
  }, [filteredVulnerabilities, vulnerabilityPage, vulnerabilityLimit])

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'text-red-600 bg-red-100'
      case 'high':
        return 'text-orange-600 bg-orange-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'low':
        return 'text-blue-600 bg-blue-100'
      case 'info':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'RUNNING':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500" />
      case 'FAILED':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !asset) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
        <p className="mt-1 text-sm text-gray-500">{error || 'Asset not found'}</p>
        <div className="mt-6">
          <Button onClick={() => window.history.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <PageContainer maxWidth="full" className="space-y-6">
        <PageHeader
          title={asset.title}
          description={`Security analysis for ${asset.domain}`}
          backButton={{
            href: "/dashboard/assets",
            label: ""
          }}
          actions={
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => handleExport('json')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button
                onClick={() => handleExport('csv')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              {assetStats?.latestScan && (
                <PDFReportGenerator
                  assetId={asset.id}
                  reportType="asset"
                  className="bg-white/80 backdrop-blur-sm"
                />
              )}
              <Button
                variant="outline"
                onClick={() => window.open(asset.url, '_blank')}
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Visit Site
              </Button>
              <Button
                onClick={() => window.location.href = `/dashboard/scan?url=${encodeURIComponent(asset.url)}`}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Search className="h-4 w-4 mr-2" />
                New Scan
              </Button>
            </div>
          }
        />

        {/* Enhanced Asset Overview Cards */}
        {assetStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Scans Card */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Scans</p>
                    <p className="text-3xl font-bold text-gray-900">{assetStats.totalScans}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {assetStats.scanFrequency.toFixed(1)} per month
                    </p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <Database className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Vulnerabilities Card */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Vulnerabilities</p>
                    <p className="text-3xl font-bold text-red-600">{assetStats.totalVulnerabilities}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Across all scans
                    </p>
                  </div>
                  <div className="p-3 bg-red-100 rounded-full">
                    <Shield className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Score Card */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Security Score</p>
                    <p className={`text-3xl font-bold ${
                      assetStats.securityScore >= 80 ? 'text-green-600' :
                      assetStats.securityScore >= 60 ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {assetStats.securityScore.toFixed(0)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Out of 100
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${
                    assetStats.securityScore >= 80 ? 'bg-green-100' :
                    assetStats.securityScore >= 60 ? 'bg-yellow-100' :
                    'bg-red-100'
                  }`}>
                    <Target className={`h-6 w-6 ${
                      assetStats.securityScore >= 80 ? 'text-green-600' :
                      assetStats.securityScore >= 60 ? 'text-yellow-600' :
                      'text-red-600'
                    }`} />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Last Scan Card */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Last Scan</p>
                    <p className="text-3xl font-bold text-purple-600">
                      <NoSSR fallback="...">
                        {assetStats.lastScanDate
                          ? new Date(assetStats.lastScanDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                          : 'Never'
                        }
                      </NoSSR>
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Avg: {formatDuration(assetStats.averageScanDuration)}
                    </p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <Clock className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Charts Section */}
        {chartData && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Vulnerability Severity Distribution */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <Shield className="h-5 w-5 mr-2 text-red-500" />
                  Severity Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                {assetStats && assetStats.totalVulnerabilities > 0 ? (
                  <div className="h-64">
                    <Doughnut data={chartData.severity} options={doughnutOptions} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                      <Shield className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                      <p>No vulnerabilities found</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Vulnerability Trends */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
                  Vulnerability Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartData.trends.labels.length > 0 ? (
                  <div className="h-64">
                    <Line data={chartData.trends} options={chartOptions} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                      <Activity className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                      <p>No trend data</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Scan History */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <BarChart3 className="h-5 w-5 mr-2 text-green-500" />
                  Scan History
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartData.history.labels.length > 0 ? (
                  <div className="h-64">
                    <Bar data={chartData.history} options={chartOptions} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                      <Calendar className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                      <p>No scan history</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Asset Information */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Globe className="h-5 w-5 mr-2 text-blue-500" />
              Asset Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">URL</dt>
                <dd className="mt-1 text-sm text-gray-900">{asset.url}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Domain</dt>
                <dd className="mt-1 text-sm text-gray-900">{asset.domain}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1">
                  <Badge variant="outline" className={`${
                    asset.status === 'ACTIVE' ? 'bg-green-50 text-green-700 border-green-200' : 'bg-gray-50 text-gray-700 border-gray-200'
                  }`}>
                    {asset.status.toLowerCase()}
                  </Badge>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Last Scanned</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  <NoSSR fallback="Loading...">
                    {asset.lastScanned ? new Date(asset.lastScanned).toLocaleString() : 'Never'}
                  </NoSSR>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Added</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  <NoSSR fallback="Loading...">
                    {new Date(asset.createdAt).toLocaleString()}
                  </NoSSR>
                </dd>
              </div>
              {asset.description && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900">{asset.description}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        {/* Enhanced Filtering and Search */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Filter className="h-5 w-5 mr-2 text-blue-500" />
              Filter & Search Vulnerabilities
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search vulnerabilities..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Severity Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Severity</label>
                <select
                  value={filters.severity}
                  onChange={(e) => handleFilterChange('severity', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Severities</option>
                  <option value="CRITICAL">Critical</option>
                  <option value="HIGH">High</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="LOW">Low</option>
                  <option value="INFO">Info</option>
                  <option value="UNKNOWN">Unknown</option>
                </select>
              </div>

              {/* Scan Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Scan</label>
                <select
                  value={filters.scanId}
                  onChange={(e) => handleFilterChange('scanId', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Scans</option>
                  {asset.scans.map(scan => (
                    <option key={scan.id} value={scan.id}>
                      Scan {scan.id.slice(0, 8)} - {scan.status}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="timestamp">Discovery Time</option>
                  <option value="severity">Severity</option>
                  <option value="name">Name</option>
                  <option value="host">Host</option>
                </select>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  Showing {paginatedVulnerabilities.length} of {filteredVulnerabilities.length} vulnerabilities
                </span>
                {filters.search && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Search: "{filters.search}"
                  </Badge>
                )}
                {filters.severity && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Severity: {filters.severity}
                  </Badge>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({
                  search: '',
                  severity: '',
                  scanId: '',
                  dateRange: '',
                  sortBy: 'timestamp',
                  sortOrder: 'desc'
                })}
                className="text-gray-600 hover:text-gray-800"
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Vulnerabilities List */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Shield className="h-5 w-5 mr-2 text-red-500" />
              Vulnerabilities ({filteredVulnerabilities.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {filteredVulnerabilities.length > 0 ? (
              <>
                <div className="w-full">
                  <table className="w-full table-fixed divide-y divide-gray-200">
                    <thead className="bg-gray-50/80">
                      <tr>
                        <th className="w-1/3 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vulnerability
                        </th>
                        <th className="w-20 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Severity
                        </th>
                        <th className="w-1/4 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Host
                        </th>
                        <th className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Template ID
                        </th>
                        <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Detected
                        </th>
                        <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {paginatedVulnerabilities.map((vuln) => (
                        <tr key={vuln.id} className="hover:bg-gray-50/50 transition-colors">
                          <td className="px-4 py-4">
                            <div>
                              <div className="text-sm font-medium text-gray-900 truncate">
                                {vuln.name}
                              </div>
                              {vuln.description && (
                                <div className="text-xs text-gray-500 truncate mt-1">
                                  {vuln.description}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <Badge variant="outline" className={`text-xs px-2 py-1 ${getSeverityColor(vuln.severity)}`}>
                              {vuln.severity.toLowerCase()}
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            <div className="flex items-center">
                              <span className="truncate">{vuln.host}</span>
                              <button
                                onClick={() => window.open(vuln.matchedAt, '_blank')}
                                className="ml-2 text-gray-400 hover:text-gray-600 flex-shrink-0"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-4 text-xs text-gray-500 truncate">
                            {vuln.templateId}
                          </td>
                          <td className="px-4 py-4 text-xs text-gray-500">
                            <NoSSR fallback="...">
                              {new Date(vuln.timestamp).toLocaleDateString()}
                            </NoSSR>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex space-x-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => navigator.clipboard.writeText(JSON.stringify(vuln, null, 2))}
                                className="text-xs px-2 py-1"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.location.href = `/dashboard/scans/${vuln.scanId}`}
                                className="text-xs px-2 py-1"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {Math.ceil(filteredVulnerabilities.length / vulnerabilityLimit) > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200 bg-gray-50/50">
                    <Pagination
                      currentPage={vulnerabilityPage}
                      totalPages={Math.ceil(filteredVulnerabilities.length / vulnerabilityLimit)}
                      totalItems={filteredVulnerabilities.length}
                      itemsPerPage={vulnerabilityLimit}
                      onPageChange={setVulnerabilityPage}
                      showInfo={true}
                      size="sm"
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <Shield className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No vulnerabilities found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filters.search || filters.severity
                    ? 'Try adjusting your filters to see more results.'
                    : 'Run a security scan to discover vulnerabilities.'
                  }
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Scan History */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Activity className="h-5 w-5 mr-2 text-green-500" />
              Scan History ({asset.scans.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {asset.scans.length > 0 ? (
              <div className="w-full">
                <table className="w-full table-fixed divide-y divide-gray-200">
                  <thead className="bg-gray-50/80">
                    <tr>
                      <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="w-1/3 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vulnerabilities
                      </th>
                      <th className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="w-1/4 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Started
                      </th>
                      <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {asset.scans.map((scan) => (
                      <tr key={scan.id} className="hover:bg-gray-50/50 transition-colors">
                        <td className="px-4 py-4">
                          <div className="flex items-center">
                            <div className={`w-3 h-3 rounded-full mr-2 ${
                              scan.status === 'COMPLETED' ? 'bg-green-400' :
                              scan.status === 'RUNNING' ? 'bg-blue-400 animate-pulse' :
                              scan.status === 'FAILED' ? 'bg-red-400' :
                              scan.status === 'PENDING' ? 'bg-yellow-400' :
                              'bg-gray-400'
                            }`} />
                            <Badge variant="outline" className={`text-xs ${
                              scan.status === 'COMPLETED' ? 'bg-green-50 text-green-700 border-green-200' :
                              scan.status === 'RUNNING' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                              scan.status === 'FAILED' ? 'bg-red-50 text-red-700 border-red-200' :
                              scan.status === 'PENDING' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                              'bg-gray-50 text-gray-700 border-gray-200'
                            }`}>
                              {scan.status.toLowerCase()}
                            </Badge>
                          </div>
                        </td>
                        <td className="px-4 py-4">
                          <div className="flex flex-wrap gap-1">
                            {scan.criticalVulns > 0 && (
                              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 text-xs px-1 py-0">
                                {scan.criticalVulns} Critical
                              </Badge>
                            )}
                            {scan.highVulns > 0 && (
                              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs px-1 py-0">
                                {scan.highVulns} High
                              </Badge>
                            )}
                            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 text-xs px-1 py-0">
                              {scan.totalVulns} Total
                            </Badge>
                          </div>
                        </td>
                        <td className="px-4 py-4 text-xs text-gray-500">
                          {formatDuration(scan.duration)}
                        </td>
                        <td className="px-4 py-4 text-xs text-gray-500">
                          <NoSSR fallback="...">
                            {scan.startedAt ? new Date(scan.startedAt).toLocaleDateString() : '-'}
                          </NoSSR>
                        </td>
                        <td className="px-4 py-4">
                          <div className="flex space-x-1">
                            <Link href={`/dashboard/scans/${scan.id}`}>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-xs px-2 py-1"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </Link>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => navigator.clipboard.writeText(JSON.stringify(scan, null, 2))}
                              className="text-xs px-2 py-1"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No scans yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Start by running a vulnerability scan on this asset.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </PageContainer>
    </div>
  )
}
