/**
 * Next.js Middleware for Authentication and Route Protection
 *
 * This middleware runs on every request to handle authentication state
 * and route protection. It ensures users are properly authenticated
 * before accessing protected routes and redirects appropriately.
 *
 * Key Features:
 * - JWT token verification from HTTP-only cookies
 * - Automatic redirection based on authentication state
 * - Protection of dashboard and admin routes
 * - Prevention of authenticated users accessing auth pages
 * - Security headers injection for enhanced protection
 *
 * Route Categories:
 * - Public: Accessible without authentication (/, /login, /signup)
 * - Auth: Login/signup pages (redirect to dashboard if authenticated)
 * - Protected: Require authentication (/dashboard/*)
 *
 * Security Headers Applied:
 * - X-Frame-Options: Prevents clickjacking attacks
 * - X-Content-Type-Options: Prevents MIME type sniffing
 * - Referrer-Policy: Controls referrer information
 * - X-XSS-Protection: Enables XSS filtering
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from './lib/auth'

/**
 * Main middleware function that handles authentication and routing
 *
 * Processes every incoming request to determine authentication state
 * and apply appropriate access controls and redirections.
 *
 * @param {NextRequest} request - Incoming HTTP request
 * @returns {Promise<NextResponse>} Response with potential redirects and security headers
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // ============================================================================
  // ROUTE CONFIGURATION
  // ============================================================================

  // Define protected routes that require authentication
  const protectedRoutes = ['/dashboard']

  // Define auth routes that should redirect authenticated users
  const authRoutes = ['/login', '/signup']

  // ============================================================================
  // ROUTE CLASSIFICATION
  // ============================================================================

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some(route =>
    pathname.startsWith(route)
  )

  // ============================================================================
  // AUTHENTICATION VERIFICATION
  // ============================================================================

  // Get the auth token from HTTP-only cookies
  const token = request.cookies.get('auth-token')?.value

  // Verify the token if it exists and determine authentication state
  let isAuthenticated = false
  if (token) {
    const payload = await verifyToken(token)
    isAuthenticated = !!payload
  }

  // ============================================================================
  // REDIRECT LOGIC
  // ============================================================================

  if (isProtectedRoute && !isAuthenticated) {
    // Redirect unauthenticated users to login page
    return NextResponse.redirect(new URL('/login', request.url))
  }

  if (isAuthRoute && isAuthenticated) {
    // Redirect authenticated users away from auth pages to dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // ============================================================================
  // SECURITY HEADERS INJECTION
  // ============================================================================

  // Create response and add security headers for enhanced protection
  const response = NextResponse.next()

  // Prevent clickjacking attacks by denying iframe embedding
  response.headers.set('X-Frame-Options', 'DENY')

  // Prevent MIME type sniffing attacks
  response.headers.set('X-Content-Type-Options', 'nosniff')

  // Control referrer information sent to external sites
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  // Enable XSS filtering in browsers (legacy but still useful)
  response.headers.set('X-XSS-Protection', '1; mode=block')

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
