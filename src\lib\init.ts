/**
 * Application Initialization
 * 
 * This module handles the initialization of background services
 * and ensures they start properly in both development and production.
 */

import { jobQueue } from './job-queue'

let isInitialized = false

/**
 * Initialize all background services
 */
export function initializeServices() {
  if (isInitialized) {
    console.log('Services already initialized, skipping...')
    return
  }

  console.log('🚀 Initializing application services...')
  
  try {
    // Initialize job queue
    jobQueue.initialize()
    
    isInitialized = true
    console.log('✅ All services initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize services:', error)
    throw error
  }
}

/**
 * Check if services are initialized
 */
export function areServicesInitialized(): boolean {
  return isInitialized
}
