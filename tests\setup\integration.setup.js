const { PrismaClient } = require('@prisma/client')

// Setup for integration tests
let prisma

beforeAll(async () => {
  // Initialize test database
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL_TEST || 'mysql://test:test@localhost:3306/ctb_scanner_test'
      }
    }
  })

  // Connect to database
  await prisma.$connect()

  // Clean database before tests
  await cleanDatabase()
})

afterAll(async () => {
  // Clean up after all tests
  await cleanDatabase()
  await prisma.$disconnect()
})

beforeEach(async () => {
  // Clean database before each test
  await cleanDatabase()
})

async function cleanDatabase() {
  // Delete in correct order to respect foreign key constraints
  await prisma.vulnerability.deleteMany()
  await prisma.scan.deleteMany()
  await prisma.asset.deleteMany()
  await prisma.user.deleteMany()
}

// Make prisma available globally in integration tests
global.testPrisma = prisma

// Mock environment variables for tests
process.env.NODE_ENV = 'test'
process.env.JWT_SECRET = 'test-jwt-secret'
process.env.DATABASE_URL = process.env.DATABASE_URL_TEST || 'mysql://test:test@localhost:3306/ctb_scanner_test'
