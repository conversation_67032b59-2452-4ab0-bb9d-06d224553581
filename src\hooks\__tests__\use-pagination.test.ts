import { renderHook, act } from '@testing-library/react'
import { usePagination } from '../use-pagination'

describe('usePagination', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => usePagination())
    
    expect(result.current.page).toBe(1)
    expect(result.current.limit).toBe(10)
    expect(result.current.offset).toBe(0)
  })

  it('should initialize with custom values', () => {
    const { result } = renderHook(() => 
      usePagination({ initialPage: 3, initialLimit: 20 })
    )
    
    expect(result.current.page).toBe(3)
    expect(result.current.limit).toBe(20)
    expect(result.current.offset).toBe(40)
  })

  it('should update page correctly', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setPage(5)
    })
    
    expect(result.current.page).toBe(5)
    expect(result.current.offset).toBe(40)
  })

  it('should update limit correctly', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setLimit(25)
    })
    
    expect(result.current.limit).toBe(25)
    expect(result.current.offset).toBe(0) // Should reset to first page
    expect(result.current.page).toBe(1)
  })

  it('should go to next page', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.nextPage()
    })
    
    expect(result.current.page).toBe(2)
    expect(result.current.offset).toBe(10)
  })

  it('should go to previous page', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 3 }))
    
    act(() => {
      result.current.prevPage()
    })
    
    expect(result.current.page).toBe(2)
    expect(result.current.offset).toBe(10)
  })

  it('should not go below page 1', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.prevPage()
    })
    
    expect(result.current.page).toBe(1)
    expect(result.current.offset).toBe(0)
  })

  it('should reset to first page', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 5 }))
    
    act(() => {
      result.current.reset()
    })
    
    expect(result.current.page).toBe(1)
    expect(result.current.offset).toBe(0)
  })

  it('should calculate total pages correctly', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setTotal(95)
    })
    
    expect(result.current.totalPages).toBe(10)
  })

  it('should handle zero total correctly', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setTotal(0)
    })
    
    expect(result.current.totalPages).toBe(0)
  })

  it('should check if has next page correctly', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setTotal(25)
      result.current.setPage(2)
    })
    
    expect(result.current.hasNextPage).toBe(true)
    
    act(() => {
      result.current.setPage(3)
    })
    
    expect(result.current.hasNextPage).toBe(false)
  })

  it('should check if has previous page correctly', () => {
    const { result } = renderHook(() => usePagination())
    
    expect(result.current.hasPrevPage).toBe(false)
    
    act(() => {
      result.current.setPage(2)
    })
    
    expect(result.current.hasPrevPage).toBe(true)
  })

  it('should provide pagination info object', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setTotal(100)
      result.current.setPage(3)
    })
    
    const paginationInfo = result.current.paginationInfo
    
    expect(paginationInfo).toEqual({
      page: 3,
      limit: 10,
      total: 100,
      pages: 10,
      hasNext: true,
      hasPrev: true,
      offset: 20,
    })
  })

  it('should handle page bounds correctly when total changes', () => {
    const { result } = renderHook(() => usePagination())
    
    act(() => {
      result.current.setTotal(100)
      result.current.setPage(10)
    })
    
    expect(result.current.page).toBe(10)
    
    // Reduce total so current page is out of bounds
    act(() => {
      result.current.setTotal(50)
    })
    
    // Should adjust to last valid page
    expect(result.current.page).toBe(5)
  })

  it('should call onChange callback when page changes', () => {
    const onChange = jest.fn()
    const { result } = renderHook(() => usePagination({ onChange }))
    
    act(() => {
      result.current.setPage(3)
    })
    
    expect(onChange).toHaveBeenCalledWith({
      page: 3,
      limit: 10,
      total: 0,
      pages: 0,
      hasNext: false,
      hasPrev: true,
      offset: 20,
    })
  })

  it('should call onChange callback when limit changes', () => {
    const onChange = jest.fn()
    const { result } = renderHook(() => usePagination({ onChange }))
    
    act(() => {
      result.current.setLimit(20)
    })
    
    expect(onChange).toHaveBeenCalledWith({
      page: 1,
      limit: 20,
      total: 0,
      pages: 0,
      hasNext: false,
      hasPrev: false,
      offset: 0,
    })
  })
})
