{"name": "ctb-scanner", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:components": "jest --testPathPattern=components", "test:api": "jest --testPathPattern=integration", "test:hooks": "jest --testPathPattern=hooks", "test:utils": "jest --testPathPattern=utils", "test:debug": "jest --no-cache --verbose", "test:update-snapshots": "jest --updateSnapshot", "db:test:setup": "dotenv -e .env.test -- prisma db push", "db:test:reset": "dotenv -e .env.test -- prisma db push --force-reset", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps", "test:validate": "node scripts/test-setup-validation.js"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.13.0", "@types/bcryptjs": "^2.4.6", "@types/jspdf": "^1.3.3", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jose": "^6.0.12", "jspdf": "^3.0.1", "lucide-react": "^0.533.0", "next": "15.4.4", "prisma": "^6.13.0", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "url-parse": "^1.5.10", "uuid": "^11.1.0", "validator": "^13.15.15", "zod": "^4.0.13"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.12", "@types/supertest": "^6.0.2", "@testing-library/react": "^16.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.5.2", "@playwright/test": "^1.49.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "supertest": "^7.0.0", "msw": "^2.6.8", "dotenv-cli": "^7.4.2", "ts-jest": "^29.2.5", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}