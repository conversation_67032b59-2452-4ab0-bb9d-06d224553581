# Component Guide - CTB Scanner

This guide provides comprehensive documentation for the atomic design system implemented in CTB Scanner.

## 🎨 Atomic Design System Overview

CTB Scanner follows atomic design principles to create a scalable, maintainable component architecture:

- **Atoms**: Basic building blocks (buttons, inputs, labels)
- **Molecules**: Simple combinations of atoms (form fields, cards)
- **Organisms**: Complex components (data tables, forms, navigation)
- **Templates**: Page-level layouts and structures

## 🔬 Atoms

### Button Component

The Button component provides a consistent interface for all clickable actions.

```tsx
import { Button } from '@/components/atoms/button'

// Basic usage
<Button>Click me</Button>

// With variants
<Button variant="destructive">Delete</Button>
<Button variant="outline">Cancel</Button>

// With loading state
<Button loading>Processing...</Button>

// With icons
<Button leftIcon={<Plus />}>Add Item</Button>
<Button rightIcon={<ArrowRight />}>Continue</Button>

// Full width
<Button fullWidth>Submit Form</Button>
```

**Props:**
- `variant`: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
- `size`: 'default' | 'sm' | 'lg' | 'icon'
- `loading`: boolean
- `leftIcon`, `rightIcon`: React.ReactNode
- `fullWidth`: boolean

### Input Component

Form input with validation states and icons.

```tsx
import { Input } from '@/components/atoms/input'

// Basic input
<Input placeholder="Enter text..." />

// With icons
<Input leftIcon={<Search />} placeholder="Search..." />
<Input rightIcon={<Eye />} type="password" />

// With error state
<Input error="This field is required" />
```

**Props:**
- `leftIcon`, `rightIcon`: React.ReactNode
- `error`: string
- All standard HTML input props

### Badge Component

Status indicators and labels.

```tsx
import { Badge } from '@/components/atoms/badge'

<Badge>Default</Badge>
<Badge variant="success">Success</Badge>
<Badge variant="warning">Warning</Badge>
<Badge variant="error">Error</Badge>
<Badge size="sm">Small</Badge>
```

## 🧪 Molecules

### FormField Component

Complete form field with label, input, and error handling.

```tsx
import { FormField } from '@/components/molecules/form-field'

<FormField
  label="Email Address"
  placeholder="Enter your email"
  error={errors.email?.message}
  required
  {...register('email')}
/>

// With hint text
<FormField
  label="Password"
  type="password"
  hint="Must be at least 8 characters"
  {...register('password')}
/>
```

**Props:**
- `label`: string
- `hint`: string
- `error`: string
- `required`: boolean
- All Input component props

### Card Component

Modular card system with header, content, and footer.

```tsx
import { Card, CardHeader, CardContent, CardFooter } from '@/components/molecules/card'

// Simple card
<Card>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
</Card>

// Full card with header and footer
<Card>
  <CardHeader
    title="Card Title"
    subtitle="Optional subtitle"
    action={<Button size="sm">Action</Button>}
  />
  <CardContent>
    <p>Main content</p>
  </CardContent>
  <CardFooter>
    <Button variant="outline">Cancel</Button>
    <Button>Save</Button>
  </CardFooter>
</Card>
```

### StatusBadge Component

Specialized badges for different status types.

```tsx
import { StatusBadge } from '@/components/molecules/status-badge'

// Scan status
<StatusBadge type="scan" status="RUNNING" />
<StatusBadge type="scan" status="COMPLETED" />

// Vulnerability severity
<StatusBadge type="severity" status="critical" />
<StatusBadge type="severity" status="high" />

// Asset status
<StatusBadge type="asset" status="ACTIVE" />
```

### Pagination Component

Reusable pagination with server-side support.

```tsx
import { Pagination } from '@/components/molecules/pagination'

<Pagination
  currentPage={page}
  totalPages={totalPages}
  onPageChange={setPage}
  pageSize={pageSize}
  onPageSizeChange={setPageSize}
  totalItems={totalItems}
/>
```

## 🦠 Organisms

### DataTable Component

Generic, reusable table with pagination and loading states.

```tsx
import { DataTable } from '@/components/organisms/data-table'

const columns = [
  { key: 'name', header: 'Name', sortable: true },
  { key: 'status', header: 'Status', render: (item) => <StatusBadge type="scan" status={item.status} /> },
  { key: 'createdAt', header: 'Created', render: (item) => formatDate(item.createdAt) }
]

<DataTable
  data={scans}
  columns={columns}
  loading={loading}
  pagination={pagination}
  onPageChange={handlePageChange}
  onSort={handleSort}
  emptyMessage="No scans found"
/>
```

### ScanForm Component

Comprehensive scan configuration form.

```tsx
import { ScanForm } from '@/components/organisms/scan-form'

<ScanForm
  onSubmit={handleScanSubmit}
  isLoading={isSubmitting}
  error={submitError}
  onReset={handleReset}
  showResetButton
/>
```

### Sidebar Component

Navigation sidebar with responsive mobile support.

```tsx
import { Sidebar } from '@/components/organisms/sidebar'

<Sidebar className="custom-sidebar-styles" />
```

## 📄 Templates

### PageLayout Component

Standardized page structure.

```tsx
import { PageLayout } from '@/components/templates/page-layout'

<PageLayout
  title="Page Title"
  subtitle="Optional description"
  action={<Button>Primary Action</Button>}
  maxWidth="7xl"
>
  <div>Page content goes here</div>
</PageLayout>
```

## 🎯 Usage Guidelines

### Component Composition

Always compose components from smaller pieces:

```tsx
// Good: Using atomic components
<Card>
  <CardHeader title="Scan Results" />
  <CardContent>
    <DataTable data={results} columns={columns} />
  </CardContent>
</Card>

// Avoid: Creating monolithic components
<ScanResultsCard data={results} />
```

### Prop Consistency

Follow consistent prop naming across components:

- `variant` for style variations
- `size` for sizing options
- `loading` for loading states
- `error` for error messages
- `disabled` for disabled states

### TypeScript Usage

Always use proper TypeScript interfaces:

```tsx
interface MyComponentProps {
  title: string
  optional?: boolean
  onAction: () => void
}

export const MyComponent: React.FC<MyComponentProps> = ({ title, optional, onAction }) => {
  // Component implementation
}
```

### Accessibility

Ensure all components are accessible:

- Use semantic HTML elements
- Include proper ARIA labels
- Support keyboard navigation
- Maintain proper color contrast

## 🔧 Customization

### Styling

Components use Tailwind CSS with consistent design tokens:

```tsx
// Use design system classes
<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
  Content
</div>

// Avoid arbitrary values
<div className="bg-[#f9f9f9] border-[1px] border-[#e5e5e5]">
  Content
</div>
```

### Extending Components

Create new components by composing existing ones:

```tsx
import { Card, CardContent } from '@/components/molecules/card'
import { Badge } from '@/components/atoms/badge'

export const AlertCard: React.FC<{ message: string; type: 'info' | 'warning' | 'error' }> = ({ 
  message, 
  type 
}) => (
  <Card variant="outlined">
    <CardContent className="flex items-center space-x-3">
      <Badge variant={type}>{type.toUpperCase()}</Badge>
      <span>{message}</span>
    </CardContent>
  </Card>
)
```

This component guide ensures consistent usage of the atomic design system throughout the CTB Scanner application.
