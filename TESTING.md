# Testing Guide

This document provides comprehensive information about the testing framework and practices used in the CTB Scanner project.

## 🧪 Testing Stack

### Frontend Testing
- **Jest**: JavaScript testing framework
- **React Testing Library**: Testing utilities for React components
- **MSW (Mock Service Worker)**: API mocking for integration tests
- **Playwright**: End-to-end testing framework

### Backend Testing
- **Jest**: Unit and integration testing
- **Supertest**: HTTP assertion library for API testing
- **Prisma Test Environment**: Database testing with isolated test database

## 📁 Test Structure

```
ctb-scanner/
├── src/
│   └── components/
│       ├── atoms/__tests__/
│       ├── molecules/__tests__/
│       └── organisms/__tests__/
├── tests/
│   ├── integration/          # API integration tests
│   ├── setup/               # Test configuration
│   └── utils/               # Test utilities and helpers
├── e2e/                     # End-to-end tests
├── jest.config.js           # Jest configuration
├── jest.setup.js            # Global test setup
└── playwright.config.ts     # Playwright configuration
```

## 🚀 Running Tests

### All Tests
```bash
npm run test:all          # Run all test suites
npm test                  # Run unit tests only
npm run test:watch        # Run tests in watch mode
npm run test:coverage     # Run tests with coverage report
```

### Specific Test Types
```bash
npm run test:unit         # Unit tests only
npm run test:integration  # Integration tests only
npm run test:e2e          # End-to-end tests only
npm run test:components   # Component tests only
npm run test:api          # API tests only
npm run test:hooks        # Custom hooks tests only
```

### Development & Debugging
```bash
npm run test:debug        # Run tests with verbose output
npm run test:update-snapshots  # Update Jest snapshots
npm run test:e2e:ui       # Run E2E tests with UI
```

## 🧩 Unit Testing

### Component Testing
Components are tested using React Testing Library with focus on:
- Rendering behavior
- User interactions
- Props validation
- Accessibility
- Error states

**Example:**
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../button'

describe('Button Component', () => {
  it('renders with default props', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### Custom Hooks Testing
Custom hooks are tested in isolation:
```typescript
import { renderHook, act } from '@testing-library/react'
import { usePagination } from '../use-pagination'

describe('usePagination', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => usePagination())
    
    expect(result.current.page).toBe(1)
    expect(result.current.limit).toBe(10)
  })
})
```

## 🔗 Integration Testing

### API Testing
API endpoints are tested using Supertest with a real Next.js server:

```javascript
const request = require('supertest')

describe('Authentication API', () => {
  it('should create a new user successfully', async () => {
    const response = await request(baseURL)
      .post('/api/auth/signup')
      .send(testUser)
      .expect(201)

    expect(response.body).toHaveProperty('user')
    expect(response.body.user.email).toBe(testUser.email)
  })
})
```

### Database Testing
- Uses isolated test database
- Automatic cleanup between tests
- Transaction rollback for data isolation

## 🎭 End-to-End Testing

### Playwright Tests
E2E tests simulate real user workflows:

```typescript
import { test, expect } from '@playwright/test'

test('should complete signup flow', async ({ page }) => {
  await page.goto('/')
  await page.getByRole('link', { name: /sign up/i }).click()
  
  // Fill form
  await page.getByPlaceholder(/email/i).fill('<EMAIL>')
  await page.getByRole('button', { name: /create account/i }).click()
  
  // Verify redirect
  await expect(page).toHaveURL('/dashboard')
})
```

### Cross-Browser Testing
Tests run on multiple browsers:
- Chromium (Chrome/Edge)
- Firefox
- WebKit (Safari)
- Mobile Chrome
- Mobile Safari

## 🛠️ Test Utilities

### Mock Data Generators
```typescript
import { createMockUser, createMockScan } from '../tests/utils/test-helpers'

const user = createMockUser({ email: '<EMAIL>' })
const scan = createMockScan({ status: 'COMPLETED' })
```

### API Mocking with MSW
```typescript
import { rest } from 'msw'
import { server } from '../tests/setup/msw.setup'

// Override default handler for specific test
server.use(
  rest.post('/api/scans', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Server error' }))
  })
)
```

## 📊 Coverage Requirements

### Coverage Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Coverage Reports
```bash
npm run test:coverage
```

Reports are generated in:
- `coverage/lcov-report/index.html` (HTML report)
- `coverage/lcov.info` (LCOV format)

## 🔧 Configuration

### Jest Configuration
- **Environment**: jsdom for components, node for API tests
- **Setup Files**: Global mocks and utilities
- **Module Mapping**: Path aliases (@/ → src/)
- **Coverage Collection**: Excludes test files and stories

### Playwright Configuration
- **Base URL**: http://localhost:3000
- **Browsers**: Chrome, Firefox, Safari, Mobile
- **Reporters**: HTML, JSON, JUnit
- **Screenshots**: On failure only
- **Videos**: Retain on failure

## 🚨 Testing Best Practices

### Unit Tests
1. **Test Behavior, Not Implementation**
   - Focus on what the component does, not how
   - Test user interactions and expected outcomes

2. **Use Descriptive Test Names**
   ```typescript
   it('should show error message when email is invalid')
   it('should disable submit button when form is invalid')
   ```

3. **Arrange, Act, Assert Pattern**
   ```typescript
   // Arrange
   const mockProps = { onClick: jest.fn() }
   
   // Act
   render(<Button {...mockProps}>Click</Button>)
   fireEvent.click(screen.getByRole('button'))
   
   // Assert
   expect(mockProps.onClick).toHaveBeenCalled()
   ```

### Integration Tests
1. **Test Real API Endpoints**
2. **Use Proper Authentication**
3. **Clean Database State**
4. **Test Error Scenarios**

### E2E Tests
1. **Test Critical User Journeys**
2. **Use Page Object Model**
3. **Handle Async Operations**
4. **Test Responsive Design**

## 🐛 Debugging Tests

### Jest Debugging
```bash
# Run specific test file
npm test -- button.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="should render"

# Debug with verbose output
npm run test:debug
```

### Playwright Debugging
```bash
# Run with UI mode
npm run test:e2e:ui

# Run specific test
npx playwright test auth.spec.ts

# Debug mode
npx playwright test --debug
```

## 📝 Writing New Tests

### Component Test Template
```typescript
import { render, screen } from '@testing-library/react'
import { ComponentName } from '../component-name'

describe('ComponentName', () => {
  it('should render correctly', () => {
    render(<ComponentName />)
    expect(screen.getByRole('...')).toBeInTheDocument()
  })

  it('should handle user interaction', () => {
    // Test implementation
  })

  it('should handle error states', () => {
    // Test implementation
  })
})
```

### API Test Template
```javascript
describe('API Endpoint', () => {
  it('should handle valid request', async () => {
    const response = await request(baseURL)
      .post('/api/endpoint')
      .send(validData)
      .expect(200)

    expect(response.body).toHaveProperty('expectedField')
  })

  it('should handle invalid request', async () => {
    const response = await request(baseURL)
      .post('/api/endpoint')
      .send(invalidData)
      .expect(400)

    expect(response.body).toHaveProperty('error')
  })
})
```

## 🎯 Continuous Integration

### GitHub Actions
Tests run automatically on:
- Pull requests
- Push to main branch
- Scheduled runs (nightly)

### Test Pipeline
1. **Install Dependencies**
2. **Setup Test Database**
3. **Run Unit Tests**
4. **Run Integration Tests**
5. **Run E2E Tests**
6. **Generate Coverage Report**
7. **Upload Artifacts**

## 📚 Additional Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [MSW Documentation](https://mswjs.io/docs/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
