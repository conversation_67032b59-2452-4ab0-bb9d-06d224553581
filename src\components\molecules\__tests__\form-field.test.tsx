import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { FormField } from '../form-field'
import { Search } from 'lucide-react'

describe('FormField Component', () => {
  it('renders with label and input', () => {
    render(<FormField label="Email Address" placeholder="Enter email" />)
    
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument()
  })

  it('shows required indicator when required', () => {
    render(<FormField label="Password" required />)
    
    expect(screen.getByText('*')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toHaveAttribute('required')
  })

  it('displays hint text', () => {
    render(
      <FormField 
        label="Password" 
        hint="Must be at least 8 characters"
      />
    )
    
    expect(screen.getByText('Must be at least 8 characters')).toBeInTheDocument()
  })

  it('displays error message', () => {
    render(
      <FormField 
        label="Email" 
        error="This field is required"
      />
    )
    
    expect(screen.getByText('This field is required')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toHaveAttribute('aria-invalid', 'true')
  })

  it('prioritizes error over hint', () => {
    render(
      <FormField 
        label="Email" 
        hint="Enter a valid email"
        error="This field is required"
      />
    )
    
    expect(screen.getByText('This field is required')).toBeInTheDocument()
    expect(screen.queryByText('Enter a valid email')).not.toBeInTheDocument()
  })

  it('passes through input props', () => {
    render(
      <FormField 
        label="Search"
        type="search"
        placeholder="Search..."
        leftIcon={<Search data-testid="search-icon" />}
      />
    )
    
    const input = screen.getByLabelText('Search')
    expect(input).toHaveAttribute('type', 'search')
    expect(input).toHaveAttribute('placeholder', 'Search...')
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
  })

  it('handles user interaction', async () => {
    const user = userEvent.setup()
    const handleChange = jest.fn()
    
    render(
      <FormField 
        label="Username"
        onChange={handleChange}
      />
    )
    
    const input = screen.getByLabelText('Username')
    await user.type(input, 'testuser')
    
    expect(input).toHaveValue('testuser')
    expect(handleChange).toHaveBeenCalled()
  })

  it('associates label with input correctly', () => {
    render(<FormField label="Email Address" />)
    
    const label = screen.getByText('Email Address')
    const input = screen.getByLabelText('Email Address')
    
    expect(label).toHaveAttribute('for', input.id)
  })

  it('applies custom className to container', () => {
    render(<FormField label="Test" className="custom-field" />)
    
    const container = screen.getByText('Test').closest('div')
    expect(container).toHaveClass('custom-field')
  })

  it('supports disabled state', () => {
    render(<FormField label="Disabled Field" disabled />)
    
    const input = screen.getByLabelText('Disabled Field')
    expect(input).toBeDisabled()
  })

  it('generates unique IDs for multiple instances', () => {
    render(
      <div>
        <FormField label="Field 1" />
        <FormField label="Field 2" />
      </div>
    )
    
    const input1 = screen.getByLabelText('Field 1')
    const input2 = screen.getByLabelText('Field 2')
    
    expect(input1.id).not.toBe(input2.id)
  })

  it('uses provided ID when specified', () => {
    render(<FormField label="Custom ID" id="custom-input-id" />)
    
    const input = screen.getByLabelText('Custom ID')
    expect(input).toHaveAttribute('id', 'custom-input-id')
  })

  it('handles textarea type', () => {
    render(<FormField label="Description" as="textarea" />)
    
    expect(screen.getByLabelText('Description')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toBeInTheDocument()
  })
})
