/**
 * Enhanced Logging Utility for CTB Scanner
 * 
 * Provides structured logging with different levels and contexts
 * for better debugging and monitoring of the scanning workflow.
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogContext {
  scanId?: string
  userId?: string
  targetUrl?: string
  component?: string
  action?: string
}

class Logger {
  private logLevel: LogLevel

  constructor() {
    // Set log level based on environment
    const envLevel = process.env.LOG_LEVEL?.toUpperCase()
    switch (envLevel) {
      case 'DEBUG':
        this.logLevel = LogLevel.DEBUG
        break
      case 'INFO':
        this.logLevel = LogLevel.INFO
        break
      case 'WARN':
        this.logLevel = LogLevel.WARN
        break
      case 'ERROR':
        this.logLevel = LogLevel.ERROR
        break
      default:
        this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString()
    const contextStr = context ? ` [${Object.entries(context).map(([k, v]) => `${k}=${v}`).join(', ')}]` : ''
    return `${timestamp} [${level}]${contextStr} ${message}`
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(this.formatMessage('DEBUG', message, context))
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage('INFO', message, context))
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message, context))
    }
  }

  error(message: string, error?: Error, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const errorStr = error ? ` - ${error.message}\n${error.stack}` : ''
      console.error(this.formatMessage('ERROR', message + errorStr, context))
    }
  }

  // Specialized logging methods for different components
  scan(message: string, scanId: string, context?: Partial<LogContext>): void {
    this.info(message, { ...context, scanId, component: 'SCAN' })
  }

  queue(message: string, context?: LogContext): void {
    this.info(message, { ...context, component: 'QUEUE' })
  }

  nuclei(message: string, context?: LogContext): void {
    this.info(message, { ...context, component: 'NUCLEI' })
  }

  api(message: string, context?: LogContext): void {
    this.info(message, { ...context, component: 'API' })
  }

  db(message: string, context?: LogContext): void {
    this.debug(message, { ...context, component: 'DATABASE' })
  }
}

// Singleton instance
export const logger = new Logger()

// Convenience functions for common logging patterns
export const logScanStart = (scanId: string, targetUrl: string, userId: string) => {
  logger.scan('Scan started', scanId, { targetUrl, userId, action: 'START' })
}

export const logScanComplete = (scanId: string, duration: number, vulnCount: number) => {
  logger.scan(`Scan completed in ${duration}s with ${vulnCount} vulnerabilities`, scanId, { action: 'COMPLETE' })
}

export const logScanError = (scanId: string, error: Error) => {
  logger.error('Scan failed', error, { scanId, component: 'SCAN', action: 'ERROR' })
}

export const logVulnerabilityFound = (scanId: string, templateId: string, severity: string) => {
  logger.scan(`Vulnerability found: ${templateId} (${severity})`, scanId, { action: 'VULNERABILITY' })
}
