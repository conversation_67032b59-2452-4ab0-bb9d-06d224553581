const request = require('supertest')
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

// Setup Next.js app for testing
const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = 0

let app, handle, server, baseURL, authCookie

beforeAll(async () => {
  app = next({ dev, hostname, port })
  handle = app.getRequestHandler()
  await app.prepare()

  server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true)
      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error('Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  })

  await new Promise((resolve) => {
    server.listen(0, (err) => {
      if (err) throw err
      const address = server.address()
      baseURL = `http://localhost:${address.port}`
      resolve()
    })
  })

  // Create and authenticate a test user
  const testUser = {
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Test Company',
    country: 'United States',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    confirmPassword: 'TestPassword123!'
  }

  await request(baseURL)
    .post('/api/auth/signup')
    .send(testUser)

  const loginResponse = await request(baseURL)
    .post('/api/auth/login')
    .send({
      email: testUser.email,
      password: testUser.password
    })

  authCookie = loginResponse.headers['set-cookie']
})

afterAll(async () => {
  if (server) {
    await new Promise((resolve) => server.close(resolve))
  }
  if (app) {
    await app.close()
  }
})

describe('Scans API', () => {
  describe('POST /api/scans', () => {
    it('should create a new scan successfully', async () => {
      const scanData = {
        url: 'https://example.com',
        scanType: 'quick',
        severity: 'info,low,medium,high,critical'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(201)

      expect(response.body).toHaveProperty('message', 'Scan initiated successfully')
      expect(response.body).toHaveProperty('scanId')
      expect(response.body).toHaveProperty('status', 'PENDING')
    })

    it('should reject invalid URL', async () => {
      const scanData = {
        url: 'invalid-url',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should reject private IP addresses', async () => {
      const scanData = {
        url: 'http://***********',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('private')
    })

    it('should reject localhost URLs', async () => {
      const scanData = {
        url: 'http://localhost:3000',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should require authentication', async () => {
      const scanData = {
        url: 'https://example.com',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .send(scanData)
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate scan type', async () => {
      const scanData = {
        url: 'https://example.com',
        scanType: 'invalid-type',
        severity: 'high,critical'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate severity levels', async () => {
      const scanData = {
        url: 'https://example.com',
        scanType: 'quick',
        severity: 'invalid-severity'
      }

      const response = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('GET /api/scans', () => {
    beforeEach(async () => {
      // Create a test scan
      await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send({
          url: 'https://test-scan.com',
          scanType: 'quick',
          severity: 'high,critical'
        })
    })

    it('should return user scans with pagination', async () => {
      const response = await request(baseURL)
        .get('/api/scans')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('scans')
      expect(response.body).toHaveProperty('pagination')
      expect(response.body.pagination).toHaveProperty('page')
      expect(response.body.pagination).toHaveProperty('pages')
      expect(response.body.pagination).toHaveProperty('total')
      expect(response.body.pagination).toHaveProperty('limit')
      expect(Array.isArray(response.body.scans)).toBe(true)
    })

    it('should support pagination parameters', async () => {
      const response = await request(baseURL)
        .get('/api/scans?page=1&limit=5')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body.pagination.page).toBe(1)
      expect(response.body.pagination.limit).toBe(5)
    })

    it('should support status filtering', async () => {
      const response = await request(baseURL)
        .get('/api/scans?status=PENDING')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('scans')
      // All returned scans should have PENDING status
      response.body.scans.forEach(scan => {
        expect(scan.status).toBe('PENDING')
      })
    })

    it('should require authentication', async () => {
      const response = await request(baseURL)
        .get('/api/scans')
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should only return user own scans', async () => {
      // Create another user
      const anotherUser = {
        firstName: 'Jane',
        lastName: 'Smith',
        companyName: 'Another Company',
        country: 'Canada',
        email: '<EMAIL>',
        password: 'AnotherPassword123!',
        confirmPassword: 'AnotherPassword123!'
      }

      await request(baseURL)
        .post('/api/auth/signup')
        .send(anotherUser)

      const anotherLoginResponse = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: anotherUser.email,
          password: anotherUser.password
        })

      const anotherAuthCookie = anotherLoginResponse.headers['set-cookie']

      // Get scans for the new user (should be empty)
      const response = await request(baseURL)
        .get('/api/scans')
        .set('Cookie', anotherAuthCookie)
        .expect(200)

      expect(response.body.scans).toHaveLength(0)
      expect(response.body.pagination.total).toBe(0)
    })
  })

  describe('GET /api/scans/[id]', () => {
    let scanId

    beforeEach(async () => {
      // Create a test scan
      const scanResponse = await request(baseURL)
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send({
          url: 'https://detail-test.com',
          scanType: 'quick',
          severity: 'high,critical'
        })

      scanId = scanResponse.body.scanId
    })

    it('should return scan details', async () => {
      const response = await request(baseURL)
        .get(`/api/scans/${scanId}`)
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('scan')
      expect(response.body.scan).toHaveProperty('id', scanId)
      expect(response.body.scan).toHaveProperty('targetUrl', 'https://detail-test.com')
      expect(response.body.scan).toHaveProperty('status')
      expect(response.body.scan).toHaveProperty('asset')
      expect(response.body.scan).toHaveProperty('vulnerabilities')
    })

    it('should return 404 for non-existent scan', async () => {
      const response = await request(baseURL)
        .get('/api/scans/non-existent-id')
        .set('Cookie', authCookie)
        .expect(404)

      expect(response.body).toHaveProperty('error')
    })

    it('should require authentication', async () => {
      const response = await request(baseURL)
        .get(`/api/scans/${scanId}`)
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should not allow access to other users scans', async () => {
      // Create another user
      const anotherUser = {
        firstName: 'Jane',
        lastName: 'Smith',
        companyName: 'Another Company',
        country: 'Canada',
        email: '<EMAIL>',
        password: 'AnotherPassword123!',
        confirmPassword: 'AnotherPassword123!'
      }

      await request(baseURL)
        .post('/api/auth/signup')
        .send(anotherUser)

      const anotherLoginResponse = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: anotherUser.email,
          password: anotherUser.password
        })

      const anotherAuthCookie = anotherLoginResponse.headers['set-cookie']

      // Try to access the scan with different user
      const response = await request(baseURL)
        .get(`/api/scans/${scanId}`)
        .set('Cookie', anotherAuthCookie)
        .expect(403)

      expect(response.body).toHaveProperty('error')
    })
  })
})
