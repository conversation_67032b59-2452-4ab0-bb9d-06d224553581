import { db } from './db'
import { nucleiScanner } from './nuclei'
import { scanEventManager } from './scan-events'

export interface ScanJob {
  id: string
  scanId: string
  targetUrl: string
  userId: string
  assetId: string
  options: {
    severity?: string[]
    tags?: string[]
    templates?: string[]
    excludeTemplates?: string[]
  }
}

class JobQueue {
  private isProcessing = false
  private processingInterval: NodeJS.Timeout | null = null

  constructor() {
    // Start processing jobs when the module loads
    this.startProcessing()
  }

  private startProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
    }

    // Process jobs every 5 seconds
    this.processingInterval = setInterval(() => {
      this.processJobs()
    }, 5000)
  }

  private async processJobs() {
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true

    try {
      // Get currently running or pending scans (to enforce global concurrency limit)
      const activeScans = await db.scan.findMany({
        where: {
          status: { in: ['RUNNING', 'PENDING'] }
        },
        select: {
          userId: true
        }
      })

      const activeUserIds = new Set(activeScans.map(s => s.userId))

      // Only allow up to 3 concurrent scans globally
      if (activeScans.length >= 3) {
        this.isProcessing = false
        return
      }

      // Find up to (3 - activeScans.length) pending scans, skipping users with active scans
      const pendingScans = await db.scan.findMany({
        where: {
          status: 'PENDING',
          userId: { notIn: Array.from(activeUserIds) }
        },
        include: {
          asset: true
        },
        orderBy: {
          createdAt: 'asc'
        },
        take: 3 - activeScans.length
      })

      // Process all eligible scans concurrently
      await Promise.all(pendingScans.map(scan => this.processScan(scan)))
    } catch (error) {
      console.error('Error processing job queue:', error)
    } finally {
      this.isProcessing = false
    }
  }

  private async processScan(scan: any) {
    try {
      console.log(`Processing scan ${scan.id} for ${scan.targetUrl}`)

      // Emit initial status
      await scanEventManager.emitStatus(scan.id, 'RUNNING', 'Initializing scan...')

      // Check if Nuclei is available
      const isNucleiAvailable = await nucleiScanner.checkNucleiInstallation()

      if (!isNucleiAvailable) {
        throw new Error('Nuclei scanner is not installed or not accessible. Please install Nuclei first.')
      }

      console.log('Using real Nuclei scanner')
      scanEventManager.emitProgress(scan.id, 'Using Nuclei scanner')

      const startTime = Date.now()

      // Execute scan with dynamic template configuration
      const scanResult = await nucleiScanner.scan({
        target: scan.targetUrl,
        severity: scan.options?.severity || ['critical', 'high', 'medium', 'low', 'info', 'unknown'],
        tags: scan.options?.tags,
        templates: scan.options?.templates,
        excludeTemplates: scan.options?.excludeTemplates,
        // Dynamic scan options
        scanType: scan.options?.scanType || 'web-api',
        scanMode: scan.options?.scanMode || 'basic',
        inputType: scan.options?.inputType || 'single'
        // No artificial constraints - let Nuclei run with its built-in settings
      }, scan.id)

      if (scanResult.success) {
        const duration = Math.round((Date.now() - startTime) / 1000)

        // Emit completion event (vulnerabilities were already streamed in real-time)
        await scanEventManager.emitComplete(scan.id, duration)

        // Update scan with final metadata
        await db.scan.update({
          where: { id: scan.id },
          data: {
            nucleiVersion: scanResult.stats.nucleiVersion,
            templateCount: scanResult.stats.totalTemplates
          }
        })

        // Update asset last scanned
        if (scan.assetId) {
          await db.asset.update({
            where: { id: scan.assetId },
            data: {
              lastScanned: new Date()
            }
          })
        }

        console.log(`Scan ${scan.id} completed successfully with ${scanResult.stats.totalVulnerabilities} vulnerabilities`)
      } else {
        const duration = Math.round((Date.now() - startTime) / 1000)

        // Emit error event
        await scanEventManager.emitStatus(scan.id, 'FAILED', scanResult.error)

        // Update scan with error
        await db.scan.update({
          where: { id: scan.id },
          data: {
            status: 'FAILED',
            completedAt: new Date(),
            duration,
            errorMessage: scanResult.error
          }
        })

        console.error(`Scan ${scan.id} failed:`, scanResult.error)
      }
    } catch (error) {
      console.error(`Error processing scan ${scan.id}:`, error)

      // Emit error event
      await scanEventManager.emitStatus(scan.id, 'FAILED', error instanceof Error ? error.message : 'Unknown error')

      // Update scan with error
      try {
        await db.scan.update({
          where: { id: scan.id },
          data: {
            status: 'FAILED',
            completedAt: new Date(),
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          }
        })
      } catch (updateError) {
        console.error('Failed to update scan status:', updateError)
      }
    }
  }

  private normalizeSeverity(severity: string): string {
    const normalized = severity.toUpperCase()
    const validSeverities = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN']
    return validSeverities.includes(normalized) ? normalized : 'UNKNOWN'
  }

  async addScanJob(job: ScanJob): Promise<void> {
    // Jobs are automatically picked up by the processing loop
    // No need to maintain a separate queue since we use the database
    console.log(`Scan job added for ${job.targetUrl}`)
  }

  async getQueueStatus(): Promise<{
    pending: number
    running: number
    completed: number
    failed: number
  }> {
    const [pending, running, completed, failed] = await Promise.all([
      db.scan.count({ where: { status: 'PENDING' } }),
      db.scan.count({ where: { status: 'RUNNING' } }),
      db.scan.count({ where: { status: 'COMPLETED' } }),
      db.scan.count({ where: { status: 'FAILED' } })
    ])

    return { pending, running, completed, failed }
  }

  stop() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
  }
}

// Singleton instance
export const jobQueue = new JobQueue()

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down job queue...')
  jobQueue.stop()
})

process.on('SIGINT', () => {
  console.log('Shutting down job queue...')
  jobQueue.stop()
})
