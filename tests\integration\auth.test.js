const request = require('supertest')
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const bcrypt = require('bcryptjs')

// Setup Next.js app for testing
const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = 0 // Use random port for testing

let app, handle, server, baseURL

beforeAll(async () => {
  app = next({ dev, hostname, port })
  handle = app.getRequestHandler()
  await app.prepare()

  server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true)
      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error('Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  })

  await new Promise((resolve) => {
    server.listen(0, (err) => {
      if (err) throw err
      const address = server.address()
      baseURL = `http://localhost:${address.port}`
      resolve()
    })
  })
})

afterAll(async () => {
  if (server) {
    await new Promise((resolve) => server.close(resolve))
  }
  if (app) {
    await app.close()
  }
})

describe('Authentication API', () => {
  const testUser = {
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Test Company',
    country: 'United States',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    confirmPassword: 'TestPassword123!'
  }

  describe('POST /api/auth/signup', () => {
    it('should create a new user successfully', async () => {
      const response = await request(baseURL)
        .post('/api/auth/signup')
        .send(testUser)
        .expect(201)

      expect(response.body).toHaveProperty('message', 'User created successfully')
      expect(response.body).toHaveProperty('user')
      expect(response.body.user).toHaveProperty('email', testUser.email)
      expect(response.body.user).not.toHaveProperty('password')
    })

    it('should reject duplicate email', async () => {
      // First signup
      await request(baseURL)
        .post('/api/auth/signup')
        .send(testUser)
        .expect(201)

      // Second signup with same email
      const response = await request(baseURL)
        .post('/api/auth/signup')
        .send(testUser)
        .expect(409)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate required fields', async () => {
      const invalidUser = { email: '<EMAIL>' }
      
      const response = await request(baseURL)
        .post('/api/auth/signup')
        .send(invalidUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate email format', async () => {
      const invalidUser = {
        ...testUser,
        email: 'invalid-email'
      }
      
      const response = await request(baseURL)
        .post('/api/auth/signup')
        .send(invalidUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate password strength', async () => {
      const weakPasswordUser = {
        ...testUser,
        email: '<EMAIL>',
        password: '123',
        confirmPassword: '123'
      }
      
      const response = await request(baseURL)
        .post('/api/auth/signup')
        .send(weakPasswordUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate password confirmation', async () => {
      const mismatchUser = {
        ...testUser,
        email: '<EMAIL>',
        confirmPassword: 'DifferentPassword123!'
      }
      
      const response = await request(baseURL)
        .post('/api/auth/signup')
        .send(mismatchUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create a test user before each login test
      await request(baseURL)
        .post('/api/auth/signup')
        .send(testUser)
    })

    it('should login successfully with valid credentials', async () => {
      const response = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200)

      expect(response.body).toHaveProperty('message', 'Login successful')
      expect(response.body).toHaveProperty('user')
      expect(response.body.user).toHaveProperty('email', testUser.email)
      expect(response.headers['set-cookie']).toBeDefined()
    })

    it('should reject invalid email', async () => {
      const response = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password
        })
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should reject invalid password', async () => {
      const response = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate required fields', async () => {
      const response = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: testUser.email
          // Missing password
        })
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('GET /api/auth/me', () => {
    let authCookie

    beforeEach(async () => {
      // Create and login user
      await request(baseURL)
        .post('/api/auth/signup')
        .send(testUser)

      const loginResponse = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })

      authCookie = loginResponse.headers['set-cookie']
    })

    it('should return user info when authenticated', async () => {
      const response = await request(baseURL)
        .get('/api/auth/me')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('user')
      expect(response.body.user).toHaveProperty('email', testUser.email)
      expect(response.body.user).not.toHaveProperty('password')
    })

    it('should reject unauthenticated requests', async () => {
      const response = await request(baseURL)
        .get('/api/auth/me')
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('POST /api/auth/logout', () => {
    let authCookie

    beforeEach(async () => {
      // Create and login user
      await request(baseURL)
        .post('/api/auth/signup')
        .send(testUser)

      const loginResponse = await request(baseURL)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })

      authCookie = loginResponse.headers['set-cookie']
    })

    it('should logout successfully', async () => {
      const response = await request(baseURL)
        .post('/api/auth/logout')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('message', 'Logout successful')
      
      // Verify cookie is cleared
      const setCookieHeader = response.headers['set-cookie']
      expect(setCookieHeader).toBeDefined()
      expect(setCookieHeader[0]).toContain('Max-Age=0')
    })

    it('should handle logout when not authenticated', async () => {
      const response = await request(baseURL)
        .post('/api/auth/logout')
        .expect(200)

      expect(response.body).toHaveProperty('message', 'Logout successful')
    })
  })
})
