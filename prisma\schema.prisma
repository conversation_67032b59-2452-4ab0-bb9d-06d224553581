// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  firstName   String   @map("first_name") @db.VarChar(100)
  lastName    String   @map("last_name") @db.VarChar(100)
  companyName String   @map("company_name") @db.VarChar(200)
  country     String   @db.VarChar(100)
  email       String   @unique @db.VarChar(255)
  password    String   @db.VarChar(255)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  assets Asset[]
  scans  Scan[]

  @@map("users")
  @@index([email])
}

model Asset {
  id          String   @id @default(cuid())
  url         String   @db.VarChar(500)
  domain      String   @db.VarChar(255)
  title       String?  @db.VarChar(500)
  description String?  @db.Text
  technology  Json?    // Store detected technologies
  status      AssetStatus @default(ACTIVE)
  lastScanned DateTime? @map("last_scanned")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userId String @map("user_id")
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  scans  Scan[]
  vulnerabilities Vulnerability[]

  @@map("assets")
  @@index([userId])
  @@index([domain])
  @@index([status])
  @@unique([userId, url])
}

model Scan {
  id          String     @id @default(cuid())
  targetUrl   String     @map("target_url") @db.VarChar(500)
  status      ScanStatus @default(PENDING)
  startedAt   DateTime?  @map("started_at")
  completedAt DateTime?  @map("completed_at")
  duration    Int?       // Duration in seconds
  totalVulns  Int        @default(0) @map("total_vulns")
  criticalVulns Int      @default(0) @map("critical_vulns")
  highVulns   Int        @default(0) @map("high_vulns")
  mediumVulns Int        @default(0) @map("medium_vulns")
  lowVulns    Int        @default(0) @map("low_vulns")
  infoVulns   Int        @default(0) @map("info_vulns")
  errorMessage String?   @map("error_message") @db.Text
  nucleiVersion String? @map("nuclei_version") @db.VarChar(50)
  templateCount Int?    @map("template_count")

  // Scan configuration options
  severity      Json?     @map("severity")        // Array of severity levels
  tags          Json?     @map("tags")            // Array of tags
  templates     Json?     @map("templates")       // Array of specific templates
  excludeTemplates Json?  @map("exclude_templates") // Array of templates to exclude
  scanType      String?   @map("scan_type") @db.VarChar(50)    // web-api, network, etc.
  scanMode      String?   @map("scan_mode") @db.VarChar(50)    // basic, deep, stealth
  inputType     String?   @map("input_type") @db.VarChar(50)   // single, bulk, cidr

  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  userId String @map("user_id")
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  assetId String? @map("asset_id")
  asset   Asset?  @relation(fields: [assetId], references: [id], onDelete: SetNull)
  vulnerabilities Vulnerability[]

  @@map("scans")
  @@index([userId])
  @@index([status])
  @@index([startedAt])
  @@index([assetId])
  @@index([scanType])
  @@index([scanMode])
}

model Vulnerability {
  id          String           @id @default(cuid())
  templateId  String           @map("template_id") @db.VarChar(255)
  name        String           @db.VarChar(500)
  severity    VulnerabilitySeverity
  description String?          @db.LongText
  reference   Json?            // Store references, CVE, CWE etc.
  tags        Json?            // Store tags array
  matcher     String?          @db.LongText
  extractedResults Json?       @map("extracted_results")
  request     String?          @db.LongText
  response    String?          @db.LongText
  curlCommand String?          @map("curl_command") @db.LongText
  host        String           @db.VarChar(500)
  matchedAt   String           @map("matched_at") @db.VarChar(500)
  timestamp   DateTime         @default(now())
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")

  // Relations
  scanId  String @map("scan_id")
  scan    Scan   @relation(fields: [scanId], references: [id], onDelete: Cascade)
  assetId String? @map("asset_id")
  asset   Asset?  @relation(fields: [assetId], references: [id], onDelete: SetNull)

  @@map("vulnerabilities")
  @@index([scanId])
  @@index([assetId])
  @@index([severity])
  @@index([templateId])
  @@index([host])
}

// Enums
enum AssetStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum ScanStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum VulnerabilitySeverity {
  CRITICAL
  HIGH
  MEDIUM
  LOW
  INFO
  UNKNOWN
}
