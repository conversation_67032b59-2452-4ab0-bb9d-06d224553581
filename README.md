# CTB Scanner - Vulnerability Scanning Platform

A comprehensive, production-ready vulnerability scanning platform built with Next.js 15, TypeScript, Prisma, MySQL, and Nuclei integration. Features atomic design system, real-time scanning, PDF report generation, and enterprise-grade security.

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npx prisma generate  # Generate Prisma client
npx prisma migrate   # Run migrations
npx prisma studio    # Open Prisma Studio
```

### Code Style
- ESLint configuration for Next.js
- TypeScript strict mode
- Prettier formatting (recommended)

## 🚀 Deployment

### Environment Variables (Production)
```env
# Database
DATABASE_URL="mysql://user:password@host:port/database"

# Authentication
JWT_SECRET="your-production-jwt-secret-256-bits"
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-production-nextauth-secret"

# Application
NODE_ENV="production"

# Nuclei Configuration (Optional)
NUCLEI_PATH="/usr/local/bin/nuclei"
NUCLEI_TEMPLATES_PATH="/opt/nuclei-templates"
```

### Production Deployment Steps

1. **Server Setup**
   ```bash
   # Install Node.js 18+
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install Nuclei
   go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
   nuclei -update-templates
   ```

2. **Database Setup**
   ```bash
   # Create production database
   mysql -u root -p
   CREATE DATABASE ctb_scanner_prod;
   CREATE USER 'ctb_user'@'%' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON ctb_scanner_prod.* TO 'ctb_user'@'%';
   FLUSH PRIVILEGES;
   ```

3. **Application Deployment**
   ```bash
   # Clone and build
   git clone <repository-url>
   cd ctb-scanner
   npm install

   # Run migrations
   npx prisma migrate deploy
   npx prisma generate

   # Build application
   npm run build

   # Start with PM2 (recommended)
   npm install -g pm2
   pm2 start npm --name "ctb-scanner" -- start
   pm2 save
   pm2 startup
   ```

### Installing Nuclei

#### Option 1: Using Go (Recommended)
```bash
go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
```

#### Option 2: Using Package Managers

**macOS (Homebrew):**
```bash
brew install nuclei
```

**Linux (Snap):**
```bash
sudo snap install nuclei
```

**Windows (Chocolatey):**
```bash
choco install nuclei
```

#### Option 3: Download Binary
Download the latest release from [Nuclei Releases](https://github.com/projectdiscovery/nuclei/releases)

#### Verify Installation
```bash
nuclei -version
```

#### Update Templates
```bash
nuclei -update-templates
```


## 🚀 Getting Started (Mac, Windows, Linux)

Follow these steps to set up the project on **macOS**, **Windows**, or **Linux**. Platform-specific commands are provided where needed.

### 1. Clone the repository

```bash
git clone <repository-url>
cd ctb-scanner
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Setup

Create a `.env` file in the root directory and configure your database connection:

```env
# Database Configuration
DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb-dev-2"

# JWT Secret (change in production)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Next.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-change-this-in-production"
```

### 4. Database Setup

#### Install MySQL

- **macOS:**
  ```bash
  brew install mysql
  brew services start mysql
  ```
- **Linux (Ubuntu/Debian):**
  ```bash
  sudo apt update
  sudo apt install mysql-server
  sudo systemctl start mysql
  ```
- **Windows:**
  - Download and install MySQL from [MySQL Installer](https://dev.mysql.com/downloads/installer/).
  - Start MySQL from the Services panel or MySQL Workbench.

#### Create Database

```sql
CREATE DATABASE ctb_dev_2;
CREATE USER 'root'@'localhost' IDENTIFIED BY 'rootroot';
GRANT ALL PRIVILEGES ON ctb_dev_2.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

#### Prisma Setup

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# (Optional) View database in Prisma Studio
npx prisma studio
```

### 5. Nuclei Scanner Setup

#### Install Nuclei

- **macOS:**
  ```bash
  brew install nuclei
  nuclei -update-templates
  ```
- **Linux:**
  ```bash
  sudo snap install nuclei
  nuclei -update-templates
  ```
- **Windows:**
  ```powershell
  choco install nuclei
  nuclei -update-templates
  ```
  Or download the binary from [Nuclei Releases](https://github.com/projectdiscovery/nuclei/releases) and add it to your PATH.

#### Verify Nuclei Installation

```bash
nuclei -version
```

### 📁 Project Structure

```
ctb-scanner/
├── src/
│   ├── app/                    # Next.js App Router pages and API routes
│   │   ├── api/               # Backend API endpoints
│   │   │   ├── auth/          # Authentication routes (login, signup, logout)
│   │   │   ├── scans/         # Scan management and real-time events
│   │   │   ├── assets/        # Asset management endpoints
│   │   │   └── vulnerabilities/ # Vulnerability data endpoints
│   │   ├── dashboard/         # Protected dashboard pages
│   │   │   ├── assets/        # Asset inventory management
│   │   │   ├── scans/         # Scan history and details
│   │   │   ├── vulnerabilities/ # Vulnerability management
│   │   │   └── scan/          # New scan creation
│   │   ├── (auth)/           # Authentication pages (login, signup)
│   │   └── globals.css       # Global styles and Tailwind imports
│   ├── components/           # React components (Atomic Design)
│   │   ├── atoms/           # Basic building blocks (Button, Input, etc.)
│   │   ├── molecules/       # Simple combinations (FormField, Card, etc.)
│   │   ├── organisms/       # Complex components (Sidebar, Tables, etc.)
│   │   ├── templates/       # Page layouts and structures
│   │   ├── features/        # Feature-specific components
│   │   ├── layout/          # Layout components (PageContainer, etc.)
│   │   └── reports/         # PDF report generation components
│   ├── lib/                 # Utility libraries and configurations
│   │   ├── auth.ts          # Authentication utilities (JWT, cookies)
│   │   ├── nuclei.ts        # Nuclei scanner integration
│   │   ├── db.ts            # Database connection (Prisma)
│   │   ├── utils.ts         # General utility functions
│   │   ├── validations.ts   # Zod validation schemas
│   │   ├── pdf-generator.ts # PDF report generation
│   │   ├── job-queue.ts     # Background job processing
│   │   └── scan-events.ts   # Real-time event management
│   ├── contexts/            # React contexts for state management
│   │   └── auth-context.tsx # Authentication state management
│   ├── hooks/               # Custom React hooks
│   │   ├── usePagination.ts # Pagination logic
│   │   └── useDebounce.ts   # Debounced input handling
│   └── types/               # TypeScript type definitions
├── prisma/                  # Database schema and migrations
│   ├── schema.prisma        # Database schema definition
│   └── migrations/          # Database migration files
├── tests/                   # Test files
│   ├── unit/               # Unit tests for components and utilities
│   ├── integration/        # API integration tests
│   └── e2e/                # End-to-end tests with Playwright
├── public/                 # Static assets
└── docs/                   # Documentation files
```

### 🏗️ Architecture Overview

#### **Frontend Architecture**
- **Framework**: Next.js 15.4.4 with App Router
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS with custom design system
- **Components**: Atomic Design methodology
- **State Management**: React Context + Custom hooks
- **Forms**: React Hook Form with Zod validation
- **Charts**: Chart.js with React wrapper

#### **Backend Architecture**
- **API**: Next.js API Routes (RESTful design)
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT tokens with HTTP-only cookies
- **Real-time**: Server-Sent Events (SSE) for live updates
- **Background Jobs**: Custom job queue system
- **Scanner Integration**: Nuclei subprocess management

#### **Security Architecture**
- **Authentication**: JWT with bcrypt password hashing
- **Authorization**: Middleware-based route protection
- **Input Validation**: Zod schemas on frontend and backend
- **Security Headers**: CSP, HSTS, XSS protection
- **Rate Limiting**: Per-endpoint and per-user limits
- **Data Protection**: SQL injection prevention via ORM

### 🔧 Development Workflow

#### **1. Adding New Features**
```bash
# Create feature branch
git checkout -b feature/new-feature-name

# Make changes following the patterns:
# - Add API routes in src/app/api/
# - Create components in appropriate atomic level
# - Add validation schemas in src/lib/validations.ts
# - Update database schema if needed
# - Write tests for new functionality

# Test your changes
npm run test
npm run test:e2e

# Commit with descriptive messages
git commit -m "feat: add new feature description"
```

#### **2. Component Development**
```typescript
// Follow atomic design principles:

// Atoms: Basic building blocks
src/components/atoms/button.tsx
src/components/atoms/input.tsx

// Molecules: Simple combinations
src/components/molecules/form-field.tsx
src/components/molecules/card.tsx

// Organisms: Complex components
src/components/organisms/sidebar.tsx
src/components/organisms/data-table.tsx

// Templates: Page layouts
src/components/templates/dashboard-layout.tsx
```

#### **3. API Development**
```typescript
// API routes follow RESTful conventions:
// GET    /api/scans          - List scans
// POST   /api/scans          - Create scan
// GET    /api/scans/[id]     - Get scan details
// PUT    /api/scans/[id]     - Update scan
// DELETE /api/scans/[id]     - Delete scan

// Always include:
// - Authentication checks
// - Input validation
// - Error handling
// - Rate limiting
// - Proper HTTP status codes
```

#### **4. Database Changes**
```bash
# Modify schema
vim prisma/schema.prisma

# Generate migration
npx prisma db push

# Update Prisma client
npx prisma generate
```

---

## 🚀 Features

### 🔐 Authentication & Security
- **Secure Authentication**: JWT-based authentication with HTTP-only cookies
- **Password Security**: Bcrypt hashing with salt rounds
- **Input Validation**: Comprehensive validation using Zod schemas
- **Rate Limiting**: Protection against brute force attacks and scan abuse
- **Security Headers**: CSRF protection, XSS prevention, and more
- **URL Validation**: Advanced URL sanitization and private IP blocking

### 🛡️ Vulnerability Scanning
- **Nuclei Integration**: Advanced vulnerability scanning with Nuclei templates
- **Real-time Scanning**: Live scan progress with WebSocket-like updates
- **Multiple Scan Types**: Quick, Deep, and Custom scan configurations
- **Template Management**: Comprehensive template categorization and filtering
- **Concurrent Scanning**: Multi-threaded scanning with rate limiting
- **Scan History**: Complete audit trail of all scanning activities
- **All Severity Levels**: Comprehensive detection (unknown, info, low, medium, high, critical)
- **Production Ready**: No artificial timeouts, real scanner data, proper failure handling

### 📊 Dashboard & Visualization
- **Interactive Charts**: Vulnerability distribution and trend analysis
- **Real-time Statistics**: Live dashboard with scan metrics
- **Asset Inventory**: Comprehensive asset management system
- **Vulnerability Database**: Searchable vulnerability repository
- **Export Capabilities**: Complete CSV/JSON export of all data (not just first page)
- **PDF Reports**: Professional pentest-style PDF reports with executive summaries
- **Premium UI**: Modern, sophisticated design with single-screen layouts

### 🏗️ Technical Excellence
- **Atomic Design System**: Modular, reusable component architecture (atoms, molecules, organisms)
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Type Safety**: Full TypeScript implementation with comprehensive interfaces
- **Database**: MySQL with Prisma ORM and optimized queries
- **Error Handling**: Comprehensive error handling and logging
- **Background Jobs**: Scalable job queue system with concurrency control
- **API Documentation**: RESTful API with comprehensive endpoints
- **Component Reusability**: Highly reusable UI components with consistent prop interfaces

## 🛠️ Tech Stack

### Core Framework
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT with jose library

### Security & Validation
- **Validation**: Zod schemas with comprehensive rules
- **Password Hashing**: bcryptjs with salt rounds
- **Rate Limiting**: Custom implementation with IP tracking
- **URL Sanitization**: Advanced security validation

### Vulnerability Scanning
- **Scanner Engine**: Nuclei vulnerability scanner
- **Job Processing**: Background job queue system
- **Result Storage**: Structured vulnerability database
- **Asset Tracking**: Comprehensive asset inventory

### UI & Visualization
- **Charts**: Recharts for data visualization
- **Forms**: React Hook Form with validation
- **Icons**: Lucide React
- **Components**: Custom UI component library

### Development & Deployment
- **Type Safety**: Full TypeScript coverage
- **Database Migrations**: Prisma migrations
- **Error Handling**: Structured error management
- **API Design**: RESTful endpoints with OpenAPI compatibility

## 📋 Prerequisites

- Node.js 18+
- MySQL 8.0+
- npm or yarn
- **Nuclei Scanner** (for vulnerability scanning)


#### (Optional) Set Nuclei Path
If you installed Nuclei in a custom location, set the path in your `.env`:

```env
NUCLEI_PATH="/usr/local/bin/nuclei" # or the path to nuclei.exe on Windows
```

### 6. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
ctb-scanner/
├── public/                        # Static assets (SVGs, images)
├── prisma/                        # Database schema and migrations
│   ├── schema.prisma
│   └── migrations/
├── scripts/                       # Utility and test scripts
│   ├── diagnose-nuclei.js
│   ├── test-api.js
│   ├── test-db.js
│   ├── test-nuclei-integration.js
│   ├── test-real-time-logs.js
│   ├── test-scan.js
│   └── test-10-second-scan.md
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── admin/
│   │   │   ├── assets/
│   │   │   ├── auth/
│   │   │   ├── dashboard/
│   │   │   ├── queue/
│   │   │   ├── scans/
│   │   │   └── vulnerabilities/
│   │   ├── dashboard/
│   │   │   ├── assets/
│   │   │   ├── profile/
│   │   │   ├── scan/
│   │   │   ├── scans/
│   │   │   ├── settings/
│   │   │   └── vulnerabilities/
│   │   ├── login/
│   │   ├── signup/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── globals.css
│   ├── components/                   # Atomic Design System
│   │   ├── atoms/                   # Basic building blocks
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── avatar.tsx
│   │   │   └── index.ts
│   │   ├── molecules/               # Combinations of atoms
│   │   │   ├── form-field.tsx
│   │   │   ├── card.tsx
│   │   │   ├── status-badge.tsx
│   │   │   ├── pagination.tsx
│   │   │   └── index.ts
│   │   ├── organisms/               # Complex UI components
│   │   │   ├── data-table.tsx
│   │   │   ├── sidebar.tsx
│   │   │   ├── scan-form.tsx
│   │   │   └── index.ts
│   │   ├── templates/               # Page-level layouts
│   │   │   ├── page-layout.tsx
│   │   │   └── index.ts
│   │   ├── features/                # Feature-specific components
│   │   ├── client-wrapper.tsx
│   │   ├── index.ts
│   │   ├── loading.tsx
│   │   ├── no-ssr.tsx
│   │   ├── protected-route.tsx
│   │   ├── sidebar.tsx
│   │   ├── features/
│   │   │   └── scan/
│   │   ├── layout/
│   │   │   ├── data-table.tsx
│   │   │   ├── index.ts
│   │   │   ├── page-container.tsx
│   │   │   └── page-header.tsx
│   │   └── ui/
│   │       ├── alert.tsx
│   │       ├── badge.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── empty-state.tsx
│   │       ├── index.ts
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── loading-spinner.tsx
│   │       ├── pagination.tsx
│   │       ├── status-badge.tsx
│   │       └── tabs.tsx
│   ├── contexts/
│   │   └── auth-context.tsx
│   ├── hooks/                       # Custom React hooks
│   │   ├── use-pagination.ts
│   │   ├── use-debounce.ts
│   │   ├── use-scan-events.ts
│   │   ├── use-api.ts
│   │   ├── use-export.ts
│   │   └── index.ts
│   ├── lib/                         # Core utilities and services
│   │   ├── constants/               # Application constants
│   │   │   └── index.ts
│   │   ├── types/                   # TypeScript type definitions
│   │   │   └── index.ts
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── errors.ts
│   │   ├── job-queue.ts
│   │   ├── nuclei.ts
│   │   ├── pdf-generator.ts         # PDF report generation
│   │   ├── process-manager.ts
│   │   ├── rate-limit.ts
│   │   ├── scan-events.ts
│   │   ├── security.ts
│   │   ├── utils.ts
│   │   ├── validations.ts
│   │   ├── index.ts                 # Barrel exports
│   │   └── __tests__/
│   │       ├── auth.test.ts
│   │       └── validations.test.ts
│   └── middleware.ts
├── temp/
│   └── nuclei/                     # Temporary scan result files
├── .env.example                    # Example environment variables
├── eslint.config.mjs
├── next-env.d.ts
├── next.config.ts
├── NUCLEI_TEMPLATE_DESIGN.md
├── package.json
├── postcss.config.mjs
├── README.md
├── simple-test.json
├── test-nuclei-comparison.js
├── test-redirect-fix.json
├── tsconfig.json
```

## 🎨 Atomic Design System

CTB Scanner implements a comprehensive atomic design system for maximum component reusability and maintainability:

### 🔬 Atoms (Basic Building Blocks)
- **Button**: Multiple variants (default, destructive, outline, secondary, ghost, link) with loading states and icons
- **Input**: Form inputs with validation states, icons, and error handling
- **Badge**: Status indicators with different variants and sizes
- **Avatar**: User avatars with fallback initials
- **Typography**: Heading and Text components with consistent styling
- **Form Elements**: Label, Textarea, Select, Checkbox with unified interfaces

### 🧪 Molecules (Component Combinations)
- **FormField**: Complete form field with label, input, and error message
- **Card**: Modular card system with Header, Content, and Footer components
- **StatusBadge**: Specialized badges for scan status, vulnerability severity, and asset status
- **Pagination**: Reusable pagination with server-side support
- **SearchInput**: Debounced search input with clear functionality
- **Alert**: Notification system with multiple variants and dismissible options

### 🦠 Organisms (Complex Components)
- **DataTable**: Generic, reusable table with pagination, sorting, and loading states
- **ScanForm**: Comprehensive scan configuration form with real-time validation
- **Sidebar**: Navigation sidebar with responsive mobile support
- **PageLayout**: Template for consistent page layouts with title, actions, and content areas

### 📄 Templates (Page-Level Layouts)
- **PageLayout**: Standardized page structure with configurable sections
- **DashboardLayout**: Dashboard-specific layout with sidebar and main content

### Benefits
- **Consistency**: Unified design language across the application
- **Reusability**: Components can be easily reused and composed
- **Maintainability**: Changes to base components propagate throughout the app
- **Type Safety**: Comprehensive TypeScript interfaces for all components
- **Scalability**: Easy to add new components following established patterns

## 🔐 Security Features

### Authentication
- JWT tokens with secure HTTP-only cookies
- Password hashing with bcrypt (12 salt rounds)
- Token expiration (7 days)
- Secure cookie configuration

### Rate Limiting
- 5 authentication attempts per 15 minutes
- 60 general requests per minute
- IP-based tracking

### Input Validation
- Comprehensive Zod schemas
- Password strength requirements
- Email format validation
- Input sanitization

### Security Headers
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin
- X-XSS-Protection: 1; mode=block
- Content Security Policy
- Strict Transport Security

## 📊 Database Schema

The application uses MySQL with Prisma ORM. Here's the complete database schema:

### Users Table
```sql
CREATE TABLE users (
  id VARCHAR(191) PRIMARY KEY,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  company_name VARCHAR(200) NOT NULL,
  country VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  INDEX idx_email (email)
);
```

### Assets Table
```sql
CREATE TABLE assets (
  id VARCHAR(191) PRIMARY KEY,
  url VARCHAR(500) NOT NULL,
  domain VARCHAR(255) NOT NULL,
  title VARCHAR(500),
  description TEXT,
  technology JSON,
  status ENUM('ACTIVE', 'INACTIVE', 'ARCHIVED') DEFAULT 'ACTIVE',
  last_scanned DATETIME(3),
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  user_id VARCHAR(191) NOT NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_domain (domain),
  INDEX idx_status (status),
  UNIQUE KEY unique_user_url (user_id, url),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Scans Table
```sql
CREATE TABLE scans (
  id VARCHAR(191) PRIMARY KEY,
  target_url VARCHAR(500) NOT NULL,
  status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING',
  started_at DATETIME(3),
  completed_at DATETIME(3),
  duration INT,
  total_vulns INT DEFAULT 0,
  critical_vulns INT DEFAULT 0,
  high_vulns INT DEFAULT 0,
  medium_vulns INT DEFAULT 0,
  low_vulns INT DEFAULT 0,
  info_vulns INT DEFAULT 0,
  error_message TEXT,
  nuclei_version VARCHAR(50),
  template_count INT,
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  user_id VARCHAR(191) NOT NULL,
  asset_id VARCHAR(191),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_started_at (started_at),
  INDEX idx_asset_id (asset_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE SET NULL
);
```

### Vulnerabilities Table
```sql
CREATE TABLE vulnerabilities (
  id VARCHAR(191) PRIMARY KEY,
  template_id VARCHAR(255) NOT NULL,
  name VARCHAR(500) NOT NULL,
  severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN') NOT NULL,
  description LONGTEXT,
  reference JSON,
  tags JSON,
  matcher LONGTEXT,
  extracted_results JSON,
  request LONGTEXT,
  response LONGTEXT,
  curl_command LONGTEXT,
  host VARCHAR(500) NOT NULL,
  matched_at VARCHAR(500) NOT NULL,
  timestamp DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  scan_id VARCHAR(191) NOT NULL,
  asset_id VARCHAR(191),
  INDEX idx_scan_id (scan_id),
  INDEX idx_asset_id (asset_id),
  INDEX idx_severity (severity),
  INDEX idx_template_id (template_id),
  INDEX idx_host (host),
  FOREIGN KEY (scan_id) REFERENCES scans(id) ON DELETE CASCADE,
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE SET NULL
);
```

### Key Features
- **Referential Integrity**: Proper foreign key relationships with cascade deletes
- **Indexing**: Strategic indexes for optimal query performance
- **JSON Fields**: Flexible storage for technology stack, references, and metadata
- **Enums**: Type-safe status and severity levels
- **Timestamps**: Automatic created/updated tracking
- **Unique Constraints**: Prevent duplicate assets per user

## 🧪 Testing Framework

The application includes a comprehensive testing framework with multiple testing layers:

### Testing Stack
- **Jest**: JavaScript testing framework for unit and integration tests
- **React Testing Library**: Component testing with focus on user behavior
- **Playwright**: End-to-end testing across multiple browsers
- **MSW (Mock Service Worker)**: API mocking for reliable tests
- **Supertest**: HTTP assertion library for API testing

### Test Types
- **Unit Tests**: Component, hook, and utility function testing
- **Integration Tests**: API endpoint and database interaction testing
- **E2E Tests**: Full user workflow testing across browsers
- **Visual Tests**: Component rendering and responsive design testing

### Running Tests
```bash
# All tests
npm run test:all

# Specific test types
npm run test:unit         # Unit tests only
npm run test:integration  # Integration tests only
npm run test:e2e          # End-to-end tests only
npm run test:components   # Component tests only
npm run test:api          # API tests only

# Development
npm run test:watch        # Watch mode
npm run test:coverage     # Coverage report
npm run test:debug        # Verbose output
```

### Coverage Requirements
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Test Structure
```
ctb-scanner/
├── src/components/**/__tests__/    # Component unit tests
├── src/hooks/__tests__/            # Custom hook tests
├── tests/integration/              # API integration tests
├── tests/utils/                    # Test utilities
├── e2e/                           # End-to-end tests
└── TESTING.md                     # Comprehensive testing guide
```

For detailed testing information, see [TESTING.md](./TESTING.md).

## 🛡️ API Endpoints

### Authentication Routes

#### POST /api/auth/signup
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "companyName": "Acme Corp",
  "country": "United States",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

#### POST /api/auth/login
Authenticate user and create session.

#### POST /api/auth/logout
Logout user and clear session.

#### GET /api/auth/me
Get current authenticated user information.

### Vulnerability Scanning Routes

#### POST /api/scans
Initiate a new vulnerability scan.

**Request Body:**
```json
{
  "url": "https://example.com",
  "severity": ["critical", "high", "medium", "low", "info"],
  "tags": ["cve", "xss", "sqli"],
  "templates": ["specific-template-id"],
  "excludeTemplates": ["template-to-exclude"]
}
```

**Response:**
```json
{
  "message": "Scan initiated successfully",
  "scanId": "scan_123",
  "status": "PENDING"
}
```

#### GET /api/scans
Get list of scans with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 50)
- `status`: Filter by scan status

#### GET /api/scans/[id]
Get detailed scan results including vulnerabilities.

#### DELETE /api/scans/[id]
Delete a scan and its associated vulnerabilities.

### Asset Management Routes

#### GET /api/assets
Get list of assets with scan statistics.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `status`: Filter by asset status
- `search`: Search by URL, domain, or title

#### POST /api/assets
Create a new asset manually.

#### GET /api/assets/[id]
Get detailed asset information with scan history.

#### PUT /api/assets/[id]
Update asset information.

#### DELETE /api/assets/[id]
Delete an asset and all associated data.

### Vulnerability Routes

#### GET /api/vulnerabilities
Get list of vulnerabilities across all assets.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `severity`: Filter by severity level
- `search`: Search by name, template ID, or host

### Dashboard Routes

#### GET /api/dashboard/stats
Get comprehensive dashboard statistics.

**Response:**
```json
{
  "overview": {
    "totalAssets": 25,
    "totalScans": 150,
    "totalVulnerabilities": 45,
    "activeScans": 2
  },
  "vulnerabilitySeverityCount": {
    "CRITICAL": 5,
    "HIGH": 12,
    "MEDIUM": 18,
    "LOW": 8,
    "INFO": 2
  },
  "recentScans": [...],
  "topVulnerableAssets": [...],
  "activityData": [...]
}
```


### Queue Management & Concurrency

#### Scan Job Queue & Concurrency
- The backend uses a built-in job queue to manage scan execution.
- **Concurrency:**
  - Up to **3 scans** can run concurrently across all users.
  - Each user can have **only 1 active scan** (RUNNING or PENDING) at a time.
  - Additional scan requests are queued and processed automatically when slots are available.
- The queue is managed in the database and is cross-platform (works on Mac, Windows, Linux).
- Scan status is updated in real-time and can be monitored via the API or dashboard.

#### GET /api/queue/status
Get current job queue status and scan concurrency.

**Response:**
```json
{
  "queue": {
    "pending": 3,
    "running": 1,
    "completed": 147,
    "failed": 2
  },
  "concurrency": {
    "maxConcurrentScans": 3,
    "maxConcurrentScansPerUser": 1
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### POST /api/scans
Initiate a new scan. If the user already has an active scan, the request will be queued and started automatically when possible. The response will indicate the scan is queued (PENDING) if not started immediately.

#### Scan Statuses
- `PENDING`: Waiting in the queue.
- `RUNNING`: Actively being scanned.
- `COMPLETED`: Finished successfully.
- `FAILED`: Scan failed.
- `CANCELLED`: Scan was cancelled by the user or system.

#### Real-Time Updates
- Scan status and progress are available via the dashboard and API endpoints.
- The backend emits real-time events for scan status changes (see `/api/scans/[id]`).

## 🎯 Usage Guide

### Getting Started

1. **Access the Application**
   - Visit http://localhost:3000
   - Create an account or sign in

2. **Dashboard Overview**
   - View security statistics and metrics
   - Monitor recent scan activity
   - Track vulnerability trends

3. **Asset Management**
   - Navigate to "Assets" to view your inventory
   - Add assets manually or through scanning
   - Monitor asset security status

4. **Vulnerability Scanning**
   - Go to "Scan" page
   - Enter target URL (must be publicly accessible)
   - Select severity levels and scan options
   - Monitor scan progress in real-time

5. **Results Analysis**
   - View detailed vulnerability reports
   - Export results for further analysis (CSV/JSON/PDF)
   - Generate professional PDF reports
   - Track remediation progress

### 📊 Export & Reporting Features

#### PDF Reports
- **Professional Layout**: Executive summary with vulnerability overview
- **Detailed Findings**: Complete vulnerability details with remediation guidance
- **Charts & Graphs**: Visual representation of security posture
- **Branding**: Clean, professional design suitable for client presentations
- **Comprehensive Data**: All scan results, asset information, and recommendations

#### Data Export Options
- **CSV Export**: Complete data export (all pages, not just current view)
- **JSON Export**: Structured data for integration with other tools
- **Filtered Exports**: Export based on current filters and search criteria
- **Bulk Operations**: Export multiple scans or assets simultaneously

#### Export Endpoints
- `GET /api/export/scans` - Export scan data
- `GET /api/export/assets` - Export asset inventory
- `GET /api/export/vulnerabilities` - Export vulnerability database
- `POST /api/reports/pdf` - Generate PDF reports

### Scanning Best Practices

- **Permission**: Only scan websites you own or have explicit permission to test
- **Rate Limiting**: Respect the built-in rate limits to avoid overwhelming targets
- **Scope**: Start with critical and high severity vulnerabilities
- **Regular Scanning**: Schedule regular scans to monitor security posture
- **Documentation**: Keep records of scan results for compliance

### Security Considerations

- **Network Access**: Ensure Nuclei can access target URLs
- **Firewall Rules**: Configure appropriate firewall rules for scanning
- **Resource Usage**: Monitor system resources during large scans
- **Data Privacy**: Scan results may contain sensitive information

## 🎨 UI Components

### Form Components
- `Button`: Customizable button with variants and loading states
- `Input`: Styled input field with validation and error handling
- `Label`: Accessible form labels with proper associations

### Layout Components
- `Sidebar`: Responsive navigation with collapsible mobile menu
- `ProtectedRoute`: Authentication-based route protection
- `ClientWrapper`: Hydration error prevention wrapper
- `NoSSR`: Server-side rendering bypass for dynamic content

### Visualization Components
- **Charts**: Recharts integration for vulnerability trends
- **Tables**: Sortable and filterable data tables
- **Status Indicators**: Real-time scan status displays
- **Progress Bars**: Scan progress visualization


### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application
COPY . .

# Build application
RUN npm run build

# Install Nuclei
RUN apk add --no-cache go git
RUN go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest

EXPOSE 3000

CMD ["npm", "start"]
```

### Production Considerations

#### Security
- Use HTTPS in production
- Configure proper firewall rules
- Implement network segmentation for scanning
- Regular security updates for Nuclei templates
- Monitor scan activities and resource usage

#### Performance
- Configure database connection pooling
- Implement Redis for session storage (optional)
- Set up load balancing for multiple instances
- Monitor memory usage during large scans
- Configure appropriate scan concurrency limits

#### Monitoring
- Set up application monitoring (e.g., New Relic, DataDog)
- Configure log aggregation (e.g., ELK stack)
- Monitor database performance
- Track scan queue metrics
- Set up alerting for failed scans

#### Backup & Recovery
- Regular database backups
- Backup scan results and configurations
- Document recovery procedures
- Test backup restoration regularly

### Scaling Considerations

#### Horizontal Scaling
- Separate scan workers from web application
- Use message queue (Redis/RabbitMQ) for job distribution
- Implement database read replicas
- Use CDN for static assets

#### Vertical Scaling
- Increase server resources for large scan volumes
- Optimize database queries and indexing
- Configure appropriate connection limits
- Monitor and tune garbage collection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please open an issue in the GitHub repository or contact the development team.
