/**
 * Scan Management API Routes
 *
 * This module handles all scan-related API operations including:
 * - Creating new vulnerability scans (POST)
 * - Retrieving scan history and details (GET)
 * - Managing scan lifecycle and status
 * - Rate limiting and user authentication
 *
 * Security Features:
 * - JWT authentication required for all operations
 * - Rate limiting: 10 requests per minute per user
 * - Input validation using Zod schemas
 * - URL validation and sanitization
 * - SQL injection prevention via Prisma ORM
 *
 * Scan Workflow:
 * 1. Validate user authentication and rate limits
 * 2. Validate and sanitize input URL and parameters
 * 3. Create or find existing asset
 * 4. Create scan record in database
 * 5. Add scan to background job queue
 * 6. Return scan details to client
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { scanUrlSchema, validateData } from '@/lib/validations'
import { handleApiError, ValidationError } from '@/lib/errors'
import { generalRateLimit } from '@/lib/rate-limit'
import { jobQueue } from '@/lib/job-queue'
import { validateScanUrl, validateScanParameters, getScanRateLimit } from '@/lib/security'

/**
 * POST /api/scans - Create a new vulnerability scan
 *
 * Creates a new scan request, validates the target URL, creates or finds
 * the associated asset, and queues the scan for background processing.
 *
 * @param {NextRequest} request - HTTP request containing scan parameters
 * @returns {Promise<NextResponse>} Scan details or error response
 */
export async function POST(request: NextRequest) {
  try {
    // ========================================================================
    // RATE LIMITING
    // ========================================================================

    // Apply rate limiting: 10 scans per minute per IP
    const rateLimitResult = generalRateLimit.check(request, 10)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many scan requests. Please try again later.' },
        { status: 429 }
      )
    }

    // ========================================================================
    // AUTHENTICATION
    // ========================================================================

    // Verify user authentication via JWT token
    const currentUser = await requireAuth()

    // ========================================================================
    // INPUT VALIDATION
    // ========================================================================

    // Parse and validate request body against schema
    const body = await request.json()
    const validation = validateData(scanUrlSchema, body)

    if (!validation.success) {
      throw new ValidationError('Invalid scan parameters')
    }

    const {
      url,
      target,
      severity,
      tags,
      templates,
      excludeTemplates,
      scanType,
      scanMode,
      inputType
    } = validation.data

    // Use target field if provided, otherwise fall back to url for backward compatibility
    const targetUrl = target || url

    // Enhanced URL validation
    const urlValidation = validateScanUrl(targetUrl)
    if (!urlValidation.isValid) {
      throw new ValidationError(urlValidation.error || 'Invalid target')
    }

    // Validate scan parameters
    const paramValidation = validateScanParameters({ severity, tags, templates, excludeTemplates })
    if (!paramValidation.isValid) {
      throw new ValidationError(paramValidation.errors.join(', '))
    }

    // Check user scan rate limits
    const rateLimits = getScanRateLimit(currentUser.userId)

    // Check concurrent scans
    const runningScans = await db.scan.count({
      where: {
        userId: currentUser.userId,
        status: 'RUNNING'
      }
    })

    if (runningScans >= rateLimits.maxConcurrentScans) {
      return NextResponse.json(
        { error: `Maximum ${rateLimits.maxConcurrentScans} concurrent scans allowed` },
        { status: 429 }
      )
    }

    // Check hourly scan limit
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    const recentScans = await db.scan.count({
      where: {
        userId: currentUser.userId,
        createdAt: {
          gte: oneHourAgo
        }
      }
    })

    if (recentScans >= rateLimits.maxScansPerHour) {
      return NextResponse.json(
        { error: `Maximum ${rateLimits.maxScansPerHour} scans per hour allowed` },
        { status: 429 }
      )
    }

    // Use sanitized URL
    const sanitizedUrl = urlValidation.sanitizedUrl!
    const domain = new URL(sanitizedUrl).hostname

    // Check if asset exists, create if not
    let asset = await db.asset.findFirst({
      where: {
        userId: currentUser.userId,
        url: targetUrl
      }
    })

    if (!asset) {
      asset = await db.asset.create({
        data: {
          url: targetUrl,
          domain,
          userId: currentUser.userId,
          title: `Asset for ${domain}`,
          status: 'ACTIVE'
        }
      })
    }

    // Create scan record
    const scan = await db.scan.create({
      data: {
        targetUrl: targetUrl,
        status: 'PENDING',
        userId: currentUser.userId,
        assetId: asset.id
      }
    })

    // Add scan to job queue
    await jobQueue.addScanJob({
      id: scan.id,
      scanId: scan.id,
      targetUrl: targetUrl,
      userId: currentUser.userId,
      assetId: asset.id,
      options: {
        severity,
        tags,
        templates,
        excludeTemplates,
        scanType,
        scanMode,
        inputType
      }
    })

    return NextResponse.json(
      {
        message: 'Scan initiated successfully',
        scanId: scan.id,
        status: 'PENDING'
      },
      { 
        status: 201,
        headers: {
          'X-Rate-Limit-Remaining': rateLimitResult.remaining.toString(),
        }
      }
    )

  } catch (error) {
    return handleApiError(error)
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)
    const status = searchParams.get('status')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      userId: currentUser.userId
    }

    if (status) {
      where.status = status.toUpperCase()
    }

    // Get scans with pagination
    const [scans, total] = await Promise.all([
      db.scan.findMany({
        where,
        include: {
          asset: {
            select: {
              id: true,
              domain: true,
              title: true
            }
          },
          _count: {
            select: {
              vulnerabilities: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      }),
      db.scan.count({ where })
    ])

    return NextResponse.json({
      scans,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    return handleApiError(error)
  }
}
